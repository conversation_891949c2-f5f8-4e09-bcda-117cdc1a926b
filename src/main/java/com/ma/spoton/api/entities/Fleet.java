package com.ma.spoton.api.entities;

import java.util.HashSet;
import java.util.Set;

//import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.ma.spoton.api.entities.Job.FleetStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "fleets",
    indexes = {@Index(name = "fleets_idx1_unit_number", columnList = "unit_number", unique = true)})
@Getter
@Setter
@ToString(callSuper = true)
public class Fleet extends BaseEntity {

  @Column(length = 100)
  private String carrier;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private Type type;

  @Column(name = "unit_number", length = 30, nullable = false)
  private String unitNumber;

  @Column
  @Lob
  private String remarks;

  @Column
  private String owner;

  @Column(name = "is_hot_trailer")
  private Boolean isHotTrailer;

  @Enumerated(EnumType.STRING)
  @Column
  private FleetStatus fleetStatus;
  
  @Column
  @Lob
  private String sequenceNumber;
  
  @ManyToMany(mappedBy = "fleets")
  private Set<Suppliers> commodity = new HashSet<>();

  @ManyToOne
  @JoinColumn(name = "spot_id")
  private Spot spot;

  @ManyToMany(mappedBy = "fleets")
  private Set<Client> clients = new HashSet<>();
  
//  @ManyToOne(cascade = CascadeType.ALL)
//  @JoinColumn(name = "carrier_id")
//  private Carriers carriers;

  public enum Type {
    TRUCK, TRAILER, CONTAINER;
  }
  
//  public void setFleetStatus(FleetStatus fleetStatus) {
//	    
//	    // If the status is "FULL", treat it as "LOADED"
//	    if (fleetStatus == FleetStatus.FULL) {
//	      this.fleetStatus = FleetStatus.LOADED;
//	    } else {
//	      this.fleetStatus = fleetStatus;
//	    }
//	  }

	 
  public FleetStatus getFleetStatus() {
	 return fleetStatus;
  }

}
