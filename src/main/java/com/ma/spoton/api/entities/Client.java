package com.ma.spoton.api.entities;

import java.util.HashSet;
import java.util.Set;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.Lob;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "clients",
    indexes = {
        @Index(name = "clients_idx1_client_name", columnList = "client_name", unique = true)})
@Getter
@Setter
@ToString(callSuper = true)
public class Client extends BaseEntity {

  @Column(name = "client_name", length = 100, nullable = false)
  private String clientName;

  @Column(length = 200, nullable = false)
  private String street;

  @Column(length = 50, nullable = false)
  private String city;

  @Column(length = 2, nullable = false)
  private String state;

  @Column(length = 15, nullable = false)
  private String zip;

  @Column(length = 3, nullable = false)
  private String country;

  @Column(length = 100, nullable = false)
  private String contactPerson;

  @Column(length = 100, nullable = false)
  private String contactEmail;

  @Column(length = 20)
  private String contactPhone;

  @Column
  @Lob
  private String remarks;

  @ManyToMany(mappedBy = "clients")
  private Set<User> users;

  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinTable(name = "clients__fleets", joinColumns = @JoinColumn(name = "client_id"),
      inverseJoinColumns = @JoinColumn(name = "fleet_id"))
  private Set<Fleet> fleets = new HashSet<>();
 
  private Boolean bol;
  
  private Boolean dvir;
  
  private Boolean accountDeactivation;
  
  @Column
  private String timeZone;
  
  private Boolean trailerAudit;
  
  private float overTime;
  
  private Boolean bucketSystem;
  
  private Boolean jobReAssign;
  
  private long driverJobReAssignAfter;
  
  private long spotterJobReAssignAfter;
  
  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinTable(name = "clients__roles", joinColumns = @JoinColumn(name = "client_id"),
      inverseJoinColumns = @JoinColumn(name = "role_id"))
  private Set<Role> roles = new HashSet<>();
}
