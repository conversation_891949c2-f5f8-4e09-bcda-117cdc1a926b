package com.ma.spoton.api.entities;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.Lob;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "jobs")
@Getter
@Setter
@ToString(callSuper = true)
public class Job extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "assigned_to_user_id")
    private User assignedTo;

    @ManyToOne
    @JoinColumn(name = "fleet_id")
    private Fleet fleet;

    @Column
    @Enumerated(EnumType.STRING)
    private FleetStatus fleetStatus;

    @Column
    @Lob
    private String description;

    // pickup

    @ManyToOne
    @JoinColumn(name = "pickup_location_id", nullable = false)
    private Location pickupLocation;

    @ManyToOne
    @JoinColumn(name = "pickup_spot_id", nullable = false)
    private Spot pickupSpot;

    @Column
    private ZonedDateTime pickupDateTime;

    @Column(name = "schedule_date_time")
    private ZonedDateTime scheduleDateTime;

    @Column(name = "is_scheduled")
    private Boolean isScheduled;

    @Column
    @Lob
    private String pickupNotes;

    // drop

    @ManyToOne
    @JoinColumn(name = "drop_location_id")
    private Location dropLocation;

    @ManyToOne
    @JoinColumn(name = "drop_spot_id")
    private Spot dropSpot;

    @Column
    private ZonedDateTime dropDateTime;

    @Column
    @Lob
    private String dropNotes;


    @Column(length = 30, nullable = false)
    @Enumerated(EnumType.STRING)
    private Priority priority;

    @Enumerated(EnumType.STRING)
    @Column(length = 30, nullable = false)
    private Status status;

    @Column
    private String sequenceAsn;
    private Boolean unsignedBol;
    private Boolean signedBol;
    @OneToMany(mappedBy = "job", fetch = FetchType.LAZY)
    private Set<Bol> bols = new HashSet<>();
    private Double temperature;
    private String climate;
    private Long queuePosition;
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private Bucket bucket;
    private ZonedDateTime assignedAt;
    @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinTable(name = "job__Previous_users", joinColumns = @JoinColumn(name = "job_id"),
            inverseJoinColumns = @JoinColumn(name = "user_id"))
    private Set<User> previouslyAssignedUsers;
    private ZonedDateTime priorityUpdatedAt;

    @ManyToOne
    @JoinColumn(name = "permanent_assigned_user")
    private User permanentlyAssignedUser; // when fallback timer is zero. assign to one driver and

    public FleetStatus getFleetStatus() {
        return fleetStatus;
    }

    public enum Priority {
        HIGH, MEDIUM, LOW;
    }

    public enum Status {
        QUEUE, OPEN, SCHEDULED, IN_TRANSIT, COMPLETED;
    }


    public enum FleetStatus {
        EMPTY, FULL, NIL;
    }

    public enum Bucket {
        BUCKET_DRIVER, BUCKET_SPOTTER, NIL;
    }
}
