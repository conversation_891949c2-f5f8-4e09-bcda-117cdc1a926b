package com.ma.spoton.api.entities;

import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "roles",
    uniqueConstraints = @UniqueConstraint(name = "roles_uk1", columnNames = "role_name"))
@Getter
@Setter
@ToString(callSuper = true, exclude = "users")
public class Role extends BaseEntity {

  @Column(name = "role_name", length = 50, nullable = false)
  private String roleName;

  @ManyToMany(mappedBy = "roles")
  private Set<Client> client;
  
  @ManyToMany(mappedBy = "roles")
  private Set<User> users;

}
