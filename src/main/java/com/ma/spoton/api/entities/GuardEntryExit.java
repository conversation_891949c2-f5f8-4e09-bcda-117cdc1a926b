package com.ma.spoton.api.entities;

import java.time.LocalDate;

//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.time.ZonedDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "guard_entry_exits")
@Getter
@Setter
@ToString(callSuper = true)
public class GuardEntryExit extends BaseEntity {

  @ManyToOne
  @JoinColumn(name = "location_id", nullable = false)
  private Location location;

  @ManyToOne
  @JoinColumn(name = "fleet_id", nullable = false)
  private Fleet fleet;

  @Column(length = 30, nullable = false)
  @Enumerated(EnumType.STRING)
  private Type type;

  @Column
  @Lob
  private String notes;
  
  @Column
  private String proNumber;
  
  @Column
  private String carrier;
  
  @Column
  private String sub; // change to id if owner 
  
  @Column
  private String loadStatus;
  
  @Column
  private String sequenceNumber;
  
  @Column
  private String tractorNumber;

  @ManyToOne
  @JoinColumn(name = "spot_id")
  private Spot spot;
  
//  @ManyToOne
//  @JoinColumn(name = "driver_id")
//  private User driver;
  
  @Column
  private String driver;
  
  @Column
  private String billOfLandingType;
  
  @Column
  private String billOfLandingImage;
  
  @ManyToOne
  @JoinColumn(name = "supplier_id")
  private Suppliers supplier;
  
  @Column
  private LocalDate dateOfPickup;
  
  @Column
  private LocalDate dueAtPlant;
  
  @Column
  private LocalDate dateOfArrival;

  public enum Type {
    ENTRY, EXIT;
  }

}
