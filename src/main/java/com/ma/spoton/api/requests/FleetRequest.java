package com.ma.spoton.api.requests;

import static com.ma.spoton.api.constants.BusinessConstants.FLEET_CARRIER_MAX_SIZE;
import static com.ma.spoton.api.constants.BusinessConstants.FLEET_UNIT_NUMBER_MAX_SIZE;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.ma.spoton.api.entities.Fleet.Type;
import com.ma.spoton.api.entities.Job.FleetStatus;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class FleetRequest {

  @Size(max = FLEET_CARRIER_MAX_SIZE,
      message = "Size.fleetRequest.carrier(::)" + FLEET_CARRIER_MAX_SIZE)
  private String carrier;

  @NotNull(message = "NotNull.fleetRequest.type")
  private Type type;

  @NotBlank(message = "NotBlank.fleetRequest.unitNumber")
  @Size(max = FLEET_UNIT_NUMBER_MAX_SIZE,
      message = "Size.fleetRequest.unitNumber(::)" + FLEET_UNIT_NUMBER_MAX_SIZE)
  private String unitNumber;

  private String remarks;

  private String owner;

  private Boolean isHotTrailer;

  private List<String> clientIds;

  private FleetStatus fleetStatus;

  private String spotId;
  
  private String sequenceNumber;
  
  private List<String> commodities;

}
