package com.ma.spoton.api.requests;

import javax.validation.constraints.NotBlank;

import com.ma.spoton.api.entities.Job.FleetStatus;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class TrailerAuditRequest {


	private String fleetId;
	
	private FleetStatus trailerStatus;
	
	private String notes;
	
	@NotBlank(message = "NotBlank.trailerAuditRequest.spotId")
	private String spotId;
	
}
