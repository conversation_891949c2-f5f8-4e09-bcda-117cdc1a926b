package com.ma.spoton.api.requests;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.ma.spoton.api.entities.Job.Bucket;
import com.ma.spoton.api.entities.Job.FleetStatus;
import com.ma.spoton.api.entities.Job.Priority;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class JobRequest {

//  @NotBlank(message = "NotBlank.jobRequest.assignedToUserId")
  private String assignedToUserId;

  private Bucket bucket;
  
  @NotBlank(message = "NotBlank.jobRequest.fleetId")
  private String fleetId;

  private FleetStatus fleetStatus;

  @NotBlank(message = "NotBlank.jobRequest.pickupLocationId")
  private String pickupLocationId;

  @NotBlank(message = "NotBlank.jobRequest.pickupSpotId")
  private String pickupSpotId;

  @NotNull(message = "NotNull.jobRequest.priority")
  private Priority priority;

  private String dropLocationId;
  private String dropSpotId;
  private String description;

  private Boolean isEdit;
  
  private String sequenceAsn;
  
  private String messageId;
  private String replyUserId;
}
