package com.ma.spoton.api.services;

import java.util.List;

import org.springframework.data.domain.Pageable;

//import com.ma.spoton.api.dtos.CarrierDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.SuppliersDto;
//import com.ma.spoton.api.requests.CarriersRequest;
import com.ma.spoton.api.requests.SuppliersRequest;
import com.querydsl.core.types.Predicate;

public interface SuppliersService {

	SuppliersDto createSupplier(SuppliersRequest suppliersRequest);
	
	PagedResponse<SuppliersDto> getSuppliers(Predicate predicate, Pageable pageable);
	
	void deleteSupplier(String supplierId);
	
	void updateSupplier(String supplierId, SuppliersRequest supplierRequest);
	
	List<SuppliersDto> getAllSuppliersList(Predicate predicate, String suppliers);
	
}
