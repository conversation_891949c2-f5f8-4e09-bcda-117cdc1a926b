package com.ma.spoton.api.services;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.ma.spoton.api.repositories.JobRepository;
import com.querydsl.core.types.Predicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class KpiService {

    @Autowired
    private JobRepository jobRepository;

    // Cache to store fetchAverageMoves results
    private static class MoveCache {
        String timeZone;
        String clientId;
        LocalDate cacheDate;
        Map<String, Map<String, Integer>> moveData;

        MoveCache(String timeZone, String clientId, LocalDate cacheDate, Map<String, Map<String, Integer>> moveData) {
            this.timeZone = timeZone;
            this.clientId = clientId;
            this.cacheDate = cacheDate;
            this.moveData = moveData;
        }

        boolean isValid(String timeZone, String clientId, LocalDate today) {
            return this.timeZone.equals(timeZone) &&
                    this.clientId.equals(clientId) &&
                    this.cacheDate.equals(today);
        }
    }

    // Cache to store getWeeklyAverageMoveTime results
    private static class WeeklyMoveTimeCache {
        String timeZone;
        String clientId;
        LocalDate cacheDate;
        Map<String, Map<String, String>> moveTimeData;

        WeeklyMoveTimeCache(String timeZone, String clientId, LocalDate cacheDate, Map<String, Map<String, String>> moveTimeData) {
            this.timeZone = timeZone;
            this.clientId = clientId;
            this.cacheDate = cacheDate;
            this.moveTimeData = moveTimeData;
        }

        boolean isValid(String timeZone, String clientId, LocalDate today) {
            return this.timeZone.equals(timeZone) &&
                    this.clientId.equals(clientId) &&
                    this.cacheDate.equals(today);
        }
    }

    // Cache to store getWeeklyAverageMoveTurnAroundTime results
    private static class TurnAroundTimeCache {
        String timeZone;
        String clientId;
        LocalDate cacheDate;
        Map<String, Map<String, String>> turnAroundTimeData;

        TurnAroundTimeCache(String timeZone, String clientId, LocalDate cacheDate, Map<String, Map<String, String>> turnAroundTimeData) {
            this.timeZone = timeZone;
            this.clientId = clientId;
            this.cacheDate = cacheDate;
            this.turnAroundTimeData = turnAroundTimeData;
        }

        boolean isValid(String timeZone, String clientId, LocalDate today) {
            return this.timeZone.equals(timeZone) &&
                    this.clientId.equals(clientId) &&
                    this.cacheDate.equals(today);
        }
    }

    // Thread-safe caches using ConcurrentHashMap
    private final ConcurrentHashMap<String, MoveCache> moveCacheMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, WeeklyMoveTimeCache> weeklyMoveTimeCacheMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, TurnAroundTimeCache> turnAroundTimeCacheMap = new ConcurrentHashMap<>();

    @Transactional(readOnly = true)
    public Map<String, Map<String, String>> getJobsForAvgMovesByDriverAndSpotter(Predicate predicate, String timeZone, String clientId, boolean refreshCache) {
        return getWeeklyAverageMoveTime(timeZone, predicate, clientId, refreshCache);
    }

    public Map<String, Map<String, String>> getWeeklyAverageMoveTime(String timeZone, Predicate predicate, String clientId, boolean refreshCache) {
        ZoneId zoneId = ZoneId.of(timeZone);
        LocalDate today = LocalDate.now(zoneId);

        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateWeeklyMoveTimeCacheKey(timeZone, clientId);
        if (!refreshCache) {
            WeeklyMoveTimeCache cache = weeklyMoveTimeCacheMap.get(cacheKey);
            if (cache != null && cache.isValid(timeZone, clientId, today)) {
                return cache.moveTimeData;
            }
        }

        // Compute the result if cache is invalid or refreshCache is true
        Map<String, Map<String, String>> roleBasedAvgMoveTime = new LinkedHashMap<>();
        roleBasedAvgMoveTime.put("DRIVER", new LinkedHashMap<>());
        roleBasedAvgMoveTime.put("SPOTTER", new LinkedHashMap<>());

        // Check move count cache
        Map<String, Map<String, Integer>> cachedMoves = getCachedMoves(timeZone, clientId, today);
        if (cachedMoves == null) {
            // If cache is invalid, call fetchAverageMoves and cache the result
            cachedMoves = fetchAverageMoves(timeZone, clientId, true).getBody(); // Force refresh if cache is invalid
            moveCacheMap.put(generateCacheKey(timeZone, clientId), new MoveCache(timeZone, clientId, today, cachedMoves));
        }

        for (int i = 6; i >= 0; i--) {
            LocalDate day = today.minusDays(i);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd");

            ZonedDateTime fromDate = day.atStartOfDay(zoneId);
            ZonedDateTime toDate = day.atTime(LocalTime.MAX).atZone(zoneId);

            String dayOfWeek = day.getDayOfWeek().name() + " (" + day.format(formatter) + ")";

            // Fetch move times
            Integer driverTotalMoveTime = jobRepository.findOverallAvgMoveTime(fromDate, toDate, "DRIVER", clientId);
            Integer spotterTotalMoveTime = jobRepository.findOverallAvgMoveTime(fromDate, toDate, "SPOTTER", clientId);

            // Use cached move counts
            int driverTotalMoves = cachedMoves.get("DRIVER").getOrDefault(dayOfWeek, 0);
            int spotterTotalMoves = cachedMoves.get("SPOTTER").getOrDefault(dayOfWeek, 0);

            double driverTime = driverTotalMoves > 0 ? (double) driverTotalMoveTime / driverTotalMoves : 0;
            double spotterTime = spotterTotalMoves > 0 ? (double) spotterTotalMoveTime / spotterTotalMoves : 0;

            int roundedDriverTime = (int) Math.ceil(driverTime);
            int roundedSpotterTime = (int) Math.ceil(spotterTime);

            roleBasedAvgMoveTime.get("DRIVER").put(dayOfWeek, String.valueOf(roundedDriverTime));
            roleBasedAvgMoveTime.get("SPOTTER").put(dayOfWeek, String.valueOf(roundedSpotterTime));
        }

        // Cache the result
        weeklyMoveTimeCacheMap.put(cacheKey, new WeeklyMoveTimeCache(timeZone, clientId, today, roleBasedAvgMoveTime));

        return roleBasedAvgMoveTime;
    }

    @Transactional(readOnly = true)
    public Map<String, Map<String, String>> getJobsForAvgTurnMovesByDriverAndSpotter(Predicate predicate, String timeZone, String clientId, boolean refreshCache) {
        return getWeeklyAverageMoveTurnAroundTime(timeZone, predicate, clientId, refreshCache);
    }

    public Map<String, Map<String, String>> getWeeklyAverageMoveTurnAroundTime(String timeZone, Predicate predicate, String clientId, boolean refreshCache) {
        ZoneId zoneId = ZoneId.of(timeZone);
        LocalDate today = LocalDate.now(zoneId);

        // Check if cache is valid only if refreshCache is false
        String cacheKey = generateTurnAroundTimeCacheKey(timeZone, clientId);
        if (!refreshCache) {
            TurnAroundTimeCache cache = turnAroundTimeCacheMap.get(cacheKey);
            if (cache != null && cache.isValid(timeZone, clientId, today)) {
                return cache.turnAroundTimeData;
            }
        }

        // Compute the result if cache is invalid or refreshCache is true
        Map<String, Map<String, String>> roleBasedAvgMoveTime = new LinkedHashMap<>();
        roleBasedAvgMoveTime.put("DRIVER", new LinkedHashMap<>());
        roleBasedAvgMoveTime.put("SPOTTER", new LinkedHashMap<>());

        for (int i = 6; i >= 0; i--) {
            LocalDate day = today.minusDays(i);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd");

            ZonedDateTime fromDate = day.atStartOfDay(zoneId);
            ZonedDateTime toDate = day.atTime(LocalTime.MAX).atZone(zoneId);

            String dayOfWeek = day.getDayOfWeek().name() + " (" + day.format(formatter) + ")";

            // Fetch move times and counts
            Integer driverTotalMoveTime = jobRepository.findOverallAvgMoveTurnTime(fromDate, toDate, "DRIVER", clientId);
            Integer driverTotalMoves = jobRepository.findTotalMovesByRoleAndDate(fromDate, toDate, "DRIVER", clientId);

            double driverTime = (driverTotalMoves != null && driverTotalMoves > 0) ? (double) driverTotalMoveTime / driverTotalMoves : 0;

            Integer spotterTotalMoveTime = jobRepository.findOverallAvgMoveTurnTime(fromDate, toDate, "SPOTTER", clientId);
            Integer spotterTotalMoves = jobRepository.findTotalMovesByRoleAndDate(fromDate, toDate, "SPOTTER", clientId);

            double spotterTime = (spotterTotalMoves != null && spotterTotalMoves > 0) ? (double) spotterTotalMoveTime / spotterTotalMoves : 0;

            int roundedDriverTime = (int) Math.ceil(driverTime);
            int roundedSpotterTime = (int) Math.ceil(spotterTime);

            roleBasedAvgMoveTime.get("DRIVER").put(dayOfWeek, String.valueOf(roundedDriverTime));
            roleBasedAvgMoveTime.get("SPOTTER").put(dayOfWeek, String.valueOf(roundedSpotterTime));
        }

        // Cache the result
        turnAroundTimeCacheMap.put(cacheKey, new TurnAroundTimeCache(timeZone, clientId, today, roleBasedAvgMoveTime));

        return roleBasedAvgMoveTime;
    }

    public ResponseEntity<Map<String, Map<String, Integer>>> fetchAverageMoves(String timeZone, String clientId, boolean refreshCache) {
        Map<String, Map<String, Integer>> roleDayCountMap = new LinkedHashMap<>();
        roleDayCountMap.put("DRIVER", new LinkedHashMap<>());
        roleDayCountMap.put("SPOTTER", new LinkedHashMap<>());

        ZoneId zoneId = ZoneId.of(timeZone);
        LocalDate today = LocalDate.now(zoneId);

        // Check cache only if refreshCache is false
        if (!refreshCache) {
            Map<String, Map<String, Integer>> cachedMoves = getCachedMoves(timeZone, clientId, today);
            if (cachedMoves != null) {
                return ResponseEntity.ok(cachedMoves);
            }
        }

        // Fetch fresh data if cache is invalid or refreshCache is true
        for (int i = 6; i >= 0; i--) {
            LocalDate day = today.minusDays(i);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd");

            ZonedDateTime fromDate = day.atStartOfDay(zoneId);
            ZonedDateTime toDate = day.atTime(LocalTime.MAX).atZone(zoneId);

            int driverMoves = jobRepository.findTotalMovesByRoleAndDate(fromDate, toDate, "DRIVER", clientId);
            int spotterMoves = jobRepository.findTotalMovesByRoleAndDate(fromDate, toDate, "SPOTTER", clientId);

            String dayOfWeek = day.getDayOfWeek().name() + " (" + day.format(formatter) + ")";

            roleDayCountMap.get("DRIVER").put(dayOfWeek, driverMoves);
            roleDayCountMap.get("SPOTTER").put(dayOfWeek, spotterMoves);
        }

        // Update cache
        moveCacheMap.put(generateCacheKey(timeZone, clientId), new MoveCache(timeZone, clientId, today, roleDayCountMap));

        return ResponseEntity.ok(roleDayCountMap);
    }

    private Map<String, Map<String, Integer>> getCachedMoves(String timeZone, String clientId, LocalDate today) {
        String cacheKey = generateCacheKey(timeZone, clientId);
        MoveCache cache = moveCacheMap.get(cacheKey);
        if (cache != null && cache.isValid(timeZone, clientId, today)) {
            return cache.moveData;
        }
        return null;
    }

    private String generateCacheKey(String timeZone, String clientId) {
        return timeZone + "::" + clientId;
    }

    private String generateWeeklyMoveTimeCacheKey(String timeZone, String clientId) {
        return timeZone + "::" + clientId;
    }

    private String generateTurnAroundTimeCacheKey(String timeZone, String clientId) {
        return timeZone + "::" + clientId;
    }
}