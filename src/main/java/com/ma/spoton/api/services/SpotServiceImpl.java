package com.ma.spoton.api.services;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.validation.ConstraintViolationException;

import com.ma.spoton.api.dtos.FleetStatusCountDto;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.hql.internal.ast.tree.IsNotNullLogicOperatorNode;
import org.hibernate.hql.internal.ast.tree.IsNullLogicOperatorNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.SpotDto;
import com.ma.spoton.api.dtos.SpotExportDto;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.Job.FleetStatus;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.QFleet;
import com.ma.spoton.api.entities.QJob;
import com.ma.spoton.api.entities.QMessage;
import com.ma.spoton.api.entities.QSpot;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.entities.Spot.Status;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.SpotMapper;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.FleetRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.MessageRepository;
import com.ma.spoton.api.repositories.SpotRepository;
import com.ma.spoton.api.requests.SpotRequest;
import com.ma.spoton.api.utils.CSVUtils;
import com.ma.spoton.api.utils.DateTimeUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

@Slf4j
@Service
public class SpotServiceImpl implements SpotService {

    @Autowired
    private LocationRepository locationRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private SpotRepository spotRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private MessageRepository messageRepository;

    @Autowired
    private SpotMapper spotMapper;

    @Autowired
    private FleetRepository fleetRepository;
    
    @Override
    @Transactional
    public void createSpot(String clientId, SpotRequest spotRequest) {
//    log.info(">> createLocation({}, {})", clientId, spotRequest);
        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));

//    log.info(">> createLocation getClientName : ({})", client.getClientName());
        Location location =
                locationRepository.findByClientAndUuid(client, spotRequest.getLocationId()).orElseThrow(
                        () -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND, spotRequest.getLocationId()));
//    log.info(">> createLocation getLocationName : ({})", location.getLocationName());
        Spot spot = spotMapper.mapToEntity(spotRequest);
//    log.info(">> createLocation getSpotName : ({})", spot.getSpotName());
        spot.setLocation(location);
        try {
            spotRepository.save(spot);
        } catch (ConstraintViolationException | DataIntegrityViolationException e) {
            log.error("Error occurred while creating spot! Reason : {}", e.getMessage());
            throw new ServiceException(ErrorCode.DUPLICATE_SPOT_NAME);
        }
    }

    @Override
    @Transactional
    public void updateSpot(String clientId, String spotId, SpotRequest spotRequest) {
//    log.info(">> updateSpot({}, {}, {})", clientId, spotId, spotRequest);
        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        Spot spot = spotRepository.findByClientAndUuid(client, spotId)
                .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, spotId));
        Location location =
                locationRepository.findByClientAndUuid(client, spotRequest.getLocationId()).orElseThrow(
                        () -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND, spotRequest.getLocationId()));
        spot = spotMapper.updateEntity(spotRequest, spot);
        if(spot.getStatus() == Status.EMPTY) {        	
        	spot.setFleet(null);
        }
        spot.setLocation(location);
        spotRepository.save(spot);
    }

    @Override
    @Transactional
    public void deleteSpot(String clientId, String spotId) {
//    log.info(">> deleteLocation({}, {})", clientId, spotId);
        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        Spot spot = spotRepository.findByClientAndUuid(client, spotId)
                .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, spotId));

        BooleanExpression jobBooleanExpression =
                QJob.job.pickupSpot.eq(spot).or(QJob.job.dropSpot.eq(spot));
        long jobCount = jobRepository.count(jobBooleanExpression);

        BooleanExpression activeJobsBooleanExpression =
                QJob.job.status.eq(Job.Status.OPEN).or(QJob.job.status.eq(Job.Status.IN_TRANSIT));
        BooleanExpression activeDropOrPickupBooleanExpression = jobBooleanExpression.and(activeJobsBooleanExpression);
        long activeJobsCounts = jobRepository.count(activeDropOrPickupBooleanExpression);

        BooleanExpression messageBooleanExpression =
                QMessage.message.pickupSpot.eq(spot).or(QMessage.message.dropSpot.eq(spot));
        long msgCount = messageRepository.count(messageBooleanExpression);

        BooleanExpression trailerBooleanExpression =
                QFleet.fleet.spot.eq(spot);
        long trailerCount = fleetRepository.count(trailerBooleanExpression);

        if (activeJobsCounts > 0) {
            throw new ServiceException(ErrorCode.DELETE_SPOT_WITH_ACTIVE_JOB);
        } else if (trailerCount > 0) {
            throw new ServiceException(ErrorCode.DELETE_SPOT_WITH_ACTIVE_TRAILER);
        } else if (msgCount > 0 || jobCount > 0) {
            spot.setIsActive(false);
        } else {
            spotRepository.delete(spot);
        }

//    if (jobCount > 0) {
//        spot.setIsActive(false);
////        throw new ServiceException(ErrorCode.DELETE_SPOT_LOCATION_WITH_ACTIVE_JOB);
//    }else if(msgCount > 0) {
//      	spot.setIsActive(false);
//    } else {
//        spotRepository.delete(spot);
//    }
    }

    @Override
    @Transactional(readOnly = true)
    public SpotDto getSpot(String clientId, String spotId, String timeZone) {

//    log.info(">> getSpot({}, {}, {})", clientId, spotId, timeZone);
        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        Spot spot = spotRepository.findByClientAndUuid(client, spotId)
                .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, spotId));
        SpotDto spotDto = spotMapper.mapToDto(spot, timeZone);

        if (Status.EMPTY.equals(spot.getStatus()) || Status.TO_BE_OCCUPIED.equals(spot.getStatus())) {
            Pageable pageable = PageRequest.of(0, 1, Sort.by(Direction.DESC, "createdDate"));
            Predicate predicate = QJob.job.pickupSpot.eq(spot);
            Page<Job> jobsPage = jobRepository.findAll(predicate, pageable);
            ZonedDateTime sinceEmptyDate = spot.getCreatedDate();
            if (CollectionUtils.isNotEmpty(jobsPage.getContent())) {
                Job job = jobsPage.getContent().get(0);
                sinceEmptyDate = job.getPickupDateTime();
            }
            Long emptiedSinceSeconds = ChronoUnit.SECONDS.between(sinceEmptyDate, ZonedDateTime.now());
            spotDto.setEmptiedSinceSeconds(emptiedSinceSeconds);
        }

        return spotDto;
    }

    @Override
    @Transactional(readOnly = true)
    public PagedResponse<SpotDto> getSpots(String clientId, Predicate predicate, Pageable pageable, String timeZone, String locationIds, String lastUpdated) {
//      log.info(">> getLocations({}, {}, {}, {})", clientId, predicate, pageable, timeZone);
//      log.info(">> predicate({})", predicate);

        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));

        BooleanExpression mandatoryPredicate = QSpot.spot.location.client.eq(client);
        predicate = predicate == null ? mandatoryPredicate : mandatoryPredicate.and(predicate);
        
        if (locationIds != null && !locationIds.isBlank()) {
			List<String> locationIdsList = Arrays.asList(locationIds.split(","));
		    BooleanExpression locationFilter = QSpot.spot.location.uuid.in(locationIdsList);
		    predicate = (predicate == null) ? locationFilter : ((BooleanExpression) predicate).and(locationFilter);
		}
        
        if (lastUpdated != null) {
        	ZonedDateTime lastUpdatedDate = DateTimeUtils
					.convertStringToLocalDate(lastUpdated, BusinessConstants.FORM_DATE_FORMAT).atTime(LocalTime.MAX)
					.atZone(ZoneId.of(timeZone));
        	BooleanExpression modifiedFilter = QSpot.spot.lastModifiedDate.goe(lastUpdatedDate);
        	predicate = Objects.nonNull(predicate) ? modifiedFilter.and(predicate) : modifiedFilter;
        }

        Page<Spot> spotsPage = spotRepository.findAll(predicate, pageable);
        Set<Long> spotIds = spotsPage.stream().map(Spot::getId).collect(Collectors.toSet());

        ZonedDateTime thirtyDaysAgo = ZonedDateTime.now().minusDays(120);
        List<Job> jobs = jobRepository.findAllLatestBySpotIds(spotIds, thirtyDaysAgo);
        
        Map<Spot, List<Job>> pickupSpotToJobMap = jobs.stream()
                .filter(j -> j.getPickupSpot() != null && j.getPickupSpot().getId() != null && spotIds.contains(j.getPickupSpot().getId()))
                .collect(Collectors.groupingBy(Job::getPickupSpot));

        Map<Spot, List<Job>> dropSpotToJobMap = jobs.stream()
                .filter(j -> j.getDropSpot() != null && j.getDropSpot().getId() != null && spotIds.contains(j.getDropSpot().getId()))
                .collect(Collectors.groupingBy(Job::getDropSpot));

        Long occupiedFullTrailerSpotCount = spotRepository.countByStatusAndFleetStatus(Status.OCCUPIED, FleetStatus.FULL, predicate);
        Long occupiedEmptyTrailerSpotCount = spotRepository.countByStatusAndFleetStatus(Status.OCCUPIED, FleetStatus.EMPTY, predicate);

        Long toBeOccupiedSpotCount = spotRepository.countByStatusAndPredicate(Status.TO_BE_OCCUPIED, predicate);
        Long toBeEmptySpotCount = spotRepository.countByStatusAndPredicate(Status.TO_BE_EMPTY, predicate);
        Long emptySpotCount = spotRepository.countByStatusAndPredicate(Status.EMPTY, predicate);
//      Long toBeOccupiedSpotCount = spotRepository.countByStatus(Status.TO_BE_OCCUPIED, predicate);
//      Long toBeEmptySpotCount = spotRepository.countByStatus(Status.TO_BE_EMPTY, predicate);
//      Long emptySpotCount = spotRepository.countByStatus(Status.EMPTY, predicate);

        return PagedResponse.<SpotDto>builder().list(spotsPage.stream().map(spot -> {
        	        
        	        Status nextJobStatus = null;
        	        String nextTrailer = null;
        	        Job nextJob = null;
        	        Set<Job> unwantedPickupJobs = new HashSet<>();
        	        Set<Job> unwantedDropoffJobs = new HashSet<>();
        	        
        	        Set<Job> pickUpJobs = spot.getPickUpJobs();
        	        if(!pickUpJobs.isEmpty()) {        	       
        	        	
        	        	for(Job job : pickUpJobs) {
        	        		if(Objects.isNull(job.getAssignedAt())) {
        	        			unwantedPickupJobs.add(job);
        	        		}
        	        	}
        	        	if(unwantedPickupJobs.size() > 0) {
        	        		pickUpJobs.removeAll(unwantedPickupJobs);
        	        	}
        	        	
        	        	for(Job job : pickUpJobs) {
            	           	if(nextJob == null) {
            	           		nextJob = job;
            	           		nextJobStatus = Status.TO_BE_EMPTY;
            	           	}else if(nextJob.getAssignedAt() != null && job.getAssignedAt() != null && nextJob.getAssignedAt().isAfter(job.getAssignedAt())){
            	           		nextJob = job;
            	           		nextJobStatus = Status.TO_BE_EMPTY;
            	           	}
            	        }
        	        }
        	        
        	        Set<Job> dropOffJobs = spot.getDropOffJobs();
        	        if(!dropOffJobs.isEmpty()) {
        	        	
        	        	for(Job job : dropOffJobs) {
        	        		if(Objects.isNull(job.getAssignedAt())) {
        	        			unwantedDropoffJobs.add(job);
        	        		}
        	        	}
        	        	if(unwantedDropoffJobs.size() > 0) {
        	        		dropOffJobs.removeAll(unwantedDropoffJobs);
        	        	}
        	        	
        	        	for(Job job : dropOffJobs) {
        	        		if(nextJob == null) {
        	        			nextJob = job;
        	        			nextJobStatus = Status.TO_BE_OCCUPIED;
        	        		}else if(nextJob.getAssignedAt() != null && job.getAssignedAt() != null && nextJob.getAssignedAt().isAfter(job.getAssignedAt())) {
        	        			nextJob = job;
            	           		nextJobStatus = Status.TO_BE_OCCUPIED;
        	        		}
        	        	}
        	        }
        	        
        	        if(nextJob != null) {
        	        	nextTrailer = nextJob.getFleet().getUnitNumber();
        	        }
        	                	        
                    SpotDto spotDto = spotMapper.mapToDto(spot, timeZone);
                    spotDto.setNextJobStatus(nextJobStatus);
                    spotDto.setNextTrailer(nextTrailer);
                  
                    processSpotDto(spot, spotDto, pickupSpotToJobMap, dropSpotToJobMap);
                    return spotDto;
                }).collect(Collectors.toList())).page(spotsPage.getNumber()).size(spotsPage.getSize())
                .totalElements(spotsPage.getTotalElements())
                .tally(Map.of("totalOccupiedFullTrailer", occupiedFullTrailerSpotCount,
                        "totalOccupiedEmptyTrailer", occupiedEmptyTrailerSpotCount,
                        "totalToBeOccupied", toBeOccupiedSpotCount,
                        "totalToBeEmpty", toBeEmptySpotCount, "totalEmpty", emptySpotCount))
                .build();
    }
    

    private void processSpotDto(Spot spot, SpotDto spotDto,
                                Map<Spot, List<Job>> pickupSpotToJobMap,
                                Map<Spot, List<Job>> dropSpotToJobMap) {

        ZonedDateTime now = ZonedDateTime.now();

        if (Status.EMPTY.equals(spot.getStatus())) {
            processEmptySpot(spot, spotDto, pickupSpotToJobMap, now);
        } else if (Status.TO_BE_OCCUPIED.equals(spot.getStatus())) {
            processToBeOccupiedSpot(spot, spotDto, pickupSpotToJobMap, dropSpotToJobMap, now);
        } else if (Status.OCCUPIED.equals(spot.getStatus())) {
            processOccupiedSpot(spot, spotDto, dropSpotToJobMap, now);
        }
    }

    private void processEmptySpot(Spot spot, SpotDto spotDto,
                                  Map<Spot, List<Job>> pickupSpotToJobMap, ZonedDateTime now) {
        Long emptiedSinceSeconds = null;

        if (spotDto.getLastEmptiedTime() != null) {
            emptiedSinceSeconds = ChronoUnit.SECONDS.between(spotDto.getLastEmptiedTime(), now);
        } else {
            ZonedDateTime sinceEmptyDate = spot.getCreatedDate();
            if (pickupSpotToJobMap.containsKey(spot) && !pickupSpotToJobMap.get(spot).isEmpty()) {
                Job job = pickupSpotToJobMap.get(spot).get(0);
                sinceEmptyDate = job.getPickupDateTime();
            }
            if (sinceEmptyDate != null) {
                emptiedSinceSeconds = ChronoUnit.SECONDS.between(sinceEmptyDate, now);
            }
        }

        spotDto.setEmptiedSinceSeconds(emptiedSinceSeconds);
    }

    private void processToBeOccupiedSpot(Spot spot, SpotDto spotDto,
                                         Map<Spot, List<Job>> pickupSpotToJobMap,
                                         Map<Spot, List<Job>> dropSpotToJobMap, ZonedDateTime now) {
        if (spot.getFleet() != null) {
            Long occupiedSinceSeconds = null;

            if (spotDto.getLastOccupiedTime() != null) {
                occupiedSinceSeconds = ChronoUnit.SECONDS.between(spotDto.getLastOccupiedTime(), now);
            } else {
                ZonedDateTime sinceOccupiedDate = spot.getCreatedDate();
                if (dropSpotToJobMap.containsKey(spot) && !dropSpotToJobMap.get(spot).isEmpty()) {
                    Job job = dropSpotToJobMap.get(spot).get(0);
                    sinceOccupiedDate = job.getDropDateTime();
                }
                if (sinceOccupiedDate != null) {
                    occupiedSinceSeconds = ChronoUnit.SECONDS.between(sinceOccupiedDate, now);
                }
            }

            spotDto.setOccupiedSinceSeconds(occupiedSinceSeconds);
        } else {
            processEmptySpot(spot, spotDto, pickupSpotToJobMap, now);
        }
    }

    private void processOccupiedSpot(Spot spot, SpotDto spotDto,
                                     Map<Spot, List<Job>> dropSpotToJobMap, ZonedDateTime now) {
        Long occupiedSinceSeconds = null;

        if (spotDto.getLastOccupiedTime() != null) {
            occupiedSinceSeconds = ChronoUnit.SECONDS.between(spotDto.getLastOccupiedTime(), now);
        } else {
            ZonedDateTime sinceOccupiedDate = spot.getCreatedDate();
            if (dropSpotToJobMap.containsKey(spot) && !dropSpotToJobMap.get(spot).isEmpty()) {
                Job job = dropSpotToJobMap.get(spot).get(0);
                sinceOccupiedDate = job.getDropDateTime();
            }
            if (sinceOccupiedDate != null) {
                occupiedSinceSeconds = ChronoUnit.SECONDS.between(sinceOccupiedDate, now);
            }
        }

        spotDto.setOccupiedSinceSeconds(occupiedSinceSeconds);
    }


//  @Override
//  @Transactional(readOnly = true)
//  public PagedResponse<SpotDto> getSpots(String clientId, Predicate predicate, Pageable pageable,
//      String timeZone) {
//    log.info(">> getLocations({}, {}, {}, {})", clientId, predicate, pageable, timeZone);
//    log.info(">> predicate({})", predicate);
//    Client client = clientRepository.findActiveByUuid(clientId)
//        .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
//
//    BooleanExpression mandatoryPredicate = QSpot.spot.location.client.eq(client);
//    if (Objects.isNull(predicate)) {
//      predicate = mandatoryPredicate;
//    } else {
//      predicate = mandatoryPredicate.and(predicate);
//    }
//    Page<Spot> spotsPage = spotRepository.findAll(predicate, pageable);
//
//    Set<Long> spotIds = spotsPage.stream().map(Spot::getId).collect(Collectors.toSet());
//
//    List<Job> pickupSpotJobs = jobRepository.findAllLatestByPickupSpotIds(spotIds);
//    Map<Spot, List<Job>> pickupSpotToJobMap =
//        pickupSpotJobs.stream().collect(Collectors.groupingBy(Job::getPickupSpot));         
//    
//    List<Job> dropSpotJobs = jobRepository.findAllLatestCompletedByDropSpotIds(spotIds);
//    Map<Spot, List<Job>> dropSpotToJobMap =
//    		dropSpotJobs.stream().collect(Collectors.groupingBy(Job::getDropSpot));
//    
//     BooleanExpression occupiedFullTrailerSpotCriteria =
//        QSpot.spot.status.eq(Status.OCCUPIED).and(QSpot.spot.fleet.isNotNull())
//            .and(QSpot.spot.fleet.fleetStatus.eq(FleetStatus.FULL)).and(predicate);
//    Long occupiedFullTrailerSpotCount = spotRepository.count(occupiedFullTrailerSpotCriteria);
//
//   BooleanExpression occupiedEmptyTrailerSpotCriteria =
//        QSpot.spot.status.eq(Status.OCCUPIED).and(QSpot.spot.fleet.isNotNull())
//            .and(QSpot.spot.fleet.fleetStatus.eq(FleetStatus.EMPTY)).and(predicate);
//    Long occupiedEmptyTrailerSpotCount = spotRepository.count(occupiedEmptyTrailerSpotCriteria);
//    
//    BooleanExpression toBeOccupiedSpotCriteria =
//        QSpot.spot.status.eq(Status.TO_BE_OCCUPIED).and(predicate);
//    Long toBeOccupiedSpotCount = spotRepository.count(toBeOccupiedSpotCriteria);
//    
//    BooleanExpression toBeEmptySpotCriteria =
//        QSpot.spot.status.eq(Status.TO_BE_EMPTY).and(predicate);
//    Long toBeEmptySpotCount = spotRepository.count(toBeEmptySpotCriteria);
//    
//    BooleanExpression emptySpotCriteria = QSpot.spot.status.eq(Status.EMPTY).and(predicate);
//    Long emptySpotCount = spotRepository.count(emptySpotCriteria);
//
//    
//    
//    return PagedResponse.<SpotDto>builder().list(spotsPage.stream().map(spot -> {
//      SpotDto spotDto = spotMapper.mapToDto(spot, timeZone);
//      
//      if (Status.EMPTY.equals(spot.getStatus())) {
//    	  Long emptiedSinceSeconds;
//    	  
//    	  //New Change
//    	  if(spotDto.getLastEmptiedTime() != null)
//    	  {
//    		   emptiedSinceSeconds = ChronoUnit.SECONDS.between(spotDto.getLastEmptiedTime(), ZonedDateTime.now());  
//    	  }
//    	  
//    	  //For old data
//    	  else
//    	  {
//    		  ZonedDateTime sinceEmptyDate = spot.getCreatedDate();
//    	        if (pickupSpotToJobMap.containsKey(spot)
//    	            && CollectionUtils.isNotEmpty(pickupSpotToJobMap.get(spot))) {
//    	          Job job = pickupSpotToJobMap.get(spot).get(0);
//    	          sinceEmptyDate = job.getPickupDateTime();
//    	        }
//    	         emptiedSinceSeconds = ChronoUnit.SECONDS.between(sinceEmptyDate, ZonedDateTime.now());
//    	     
//    	  }
//    	  spotDto.setEmptiedSinceSeconds(emptiedSinceSeconds);
//    	  
//      }
//      
//      
//      if (Status.TO_BE_OCCUPIED.equals(spot.getStatus())) {
//    	  if(spot.getFleet() != null) {
//    		  Long occupiedSinceSeconds; 
//    		  if(spotDto.getLastOccupiedTime() != null)
//    	    	 {
//    	    		 occupiedSinceSeconds = ChronoUnit.SECONDS.between(spotDto.getLastOccupiedTime(), ZonedDateTime.now()); 
//    	    	 }
//    		  else
//    		  {
//    			  ZonedDateTime sinceOccupiedDate = spot.getCreatedDate(); 
//            	  if (dropSpotToJobMap.containsKey(spot)
//            	            && CollectionUtils.isNotEmpty(dropSpotToJobMap.get(spot))) {
//            	          Job job = dropSpotToJobMap.get(spot).get(0);
//            	          sinceOccupiedDate = job.getDropDateTime();
//            	  } 
//            	  occupiedSinceSeconds = ChronoUnit.SECONDS.between(sinceOccupiedDate, ZonedDateTime.now());
//    			  
//    		  }
//    		  
//    		 
//              spotDto.setOccupiedSinceSeconds(occupiedSinceSeconds); 
//    		  
//    	  }
//    	  else
//    	  {
//    		  
//    		  Long emptiedSinceSeconds;
//    		  if(spotDto.getLastEmptiedTime() != null)
//        	  {
//        		   emptiedSinceSeconds = ChronoUnit.SECONDS.between(spotDto.getLastEmptiedTime(), ZonedDateTime.now());  
//        	  }
//    		  else
//    		  {
//    			   ZonedDateTime sinceEmptyDate = spot.getCreatedDate();
//     	          if (pickupSpotToJobMap.containsKey(spot)
//     	              && CollectionUtils.isNotEmpty(pickupSpotToJobMap.get(spot))) {
//     	            Job job = pickupSpotToJobMap.get(spot).get(0);
//     	            sinceEmptyDate = job.getPickupDateTime();
//     	          }
//     	           emptiedSinceSeconds = ChronoUnit.SECONDS.between(sinceEmptyDate, ZonedDateTime.now()); 
//    		  }
//    		
//    	          spotDto.setEmptiedSinceSeconds(emptiedSinceSeconds);
//    	  }
//       
//        }
//      if (Status.OCCUPIED.equals(spot.getStatus())) {
//    	  Long occupiedSinceSeconds;
//    	  
//    	 if(spotDto.getLastOccupiedTime() != null)
//    	 {
//    		 occupiedSinceSeconds = ChronoUnit.SECONDS.between(spotDto.getLastOccupiedTime(), ZonedDateTime.now()); 
//    	 }
//    	 else
//    	 {
//    		 ZonedDateTime sinceOccupiedDate = spot.getCreatedDate(); 
//       	  if (dropSpotToJobMap.containsKey(spot)
//       	            && CollectionUtils.isNotEmpty(dropSpotToJobMap.get(spot))) {
//       	          Job job = dropSpotToJobMap.get(spot).get(0);
//       	          sinceOccupiedDate = job.getDropDateTime();
//       	  } 
//       	  occupiedSinceSeconds = ChronoUnit.SECONDS.between(sinceOccupiedDate, ZonedDateTime.now());
//             
//    	 }
//    	 spotDto.setOccupiedSinceSeconds(occupiedSinceSeconds); 
//    	  
//      }
//      
//      return spotDto;
//    }).collect(Collectors.toList())).page(spotsPage.getNumber()).size(spotsPage.getSize())
//        .totalElements(spotsPage.getTotalElements())
//        .tally(Map.of("totalOccupiedFullTrailer", occupiedFullTrailerSpotCount,
//            "totalOccupiedEmptyTrailer", occupiedEmptyTrailerSpotCount, "totalToBeOccupied",
//            toBeOccupiedSpotCount, "totalToBeEmpty", toBeEmptySpotCount, "totalEmpty",
//            emptySpotCount))
//        .build();
//  }

    @Override
    @Transactional(readOnly = true)
    public Resource exportSpotsAsCSV(String clientId, Predicate predicate, String timeZone) {
//    log.info(">> exportSpotsAsCSV({}, {}, {})", clientId, predicate, timeZone);
        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        BooleanExpression mandatoryPredicate = QSpot.spot.location.client.eq(client);
        if (Objects.isNull(predicate)) {
            predicate = mandatoryPredicate;
        } else {
            predicate = mandatoryPredicate.and(predicate);
        }
        var spots = spotRepository.findAll(predicate);

        Set<Long> spotIds = new HashSet<>();
        spots.forEach(spot -> spotIds.add(spot.getId()));

        List<Job> pickupSpotJobs = jobRepository.findAllLatestByPickupSpotIds(spotIds);
        Map<Spot, List<Job>> pickupSpotToJobMap =
                pickupSpotJobs.stream().collect(Collectors.groupingBy(Job::getPickupSpot));

        List<SpotExportDto> spotExportDtos = new ArrayList<>();
        spots.forEach(spot -> {
            SpotExportDto spotDto = spotMapper.mapToExportDto(spot, timeZone);
            if (Status.EMPTY.equals(spot.getStatus()) || Status.TO_BE_OCCUPIED.equals(spot.getStatus())) {
                ZonedDateTime sinceEmptyDate = spot.getCreatedDate();
                if (pickupSpotToJobMap.containsKey(spot)
                        && CollectionUtils.isNotEmpty(pickupSpotToJobMap.get(spot))) {
                    Job job = pickupSpotToJobMap.get(spot).get(0);
                    sinceEmptyDate = job.getPickupDateTime();
                }
                Long emptiedSinceSeconds = ChronoUnit.SECONDS.between(sinceEmptyDate, ZonedDateTime.now());
                spotDto.setEmptiedSinceSeconds(emptiedSinceSeconds);
            }
            spotExportDtos.add(spotDto);
        });

        try {
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName =
                    tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".csv";
            try (OutputStream outputStream = new FileOutputStream(fileName);) {
                String csvData = CSVUtils.toCSV(spotExportDtos, ',', true);
                outputStream.write(csvData.getBytes());
                outputStream.flush();
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting CSV!", e);
            throw new ServiceException(ErrorCode.SPOT_EXPORT, e.getMessage());
        }
    }


    @Override
    @Transactional(readOnly = true)
    public Resource exportSpotsAsEXCEL(String clientId, Predicate predicate, String timeZone) {
        // TODO Auto-generated method stub
//	  log.info(">> exportSpotsAsEXCEL({}, {}, {})", clientId, predicate, timeZone);
        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        BooleanExpression mandatoryPredicate = QSpot.spot.location.client.eq(client);
        if (Objects.isNull(predicate)) {
            predicate = mandatoryPredicate;
        } else {
            predicate = mandatoryPredicate.and(predicate);
        }
        var spots = spotRepository.findAll(predicate);

        Set<Long> spotIds = new HashSet<>();
        spots.forEach(spot -> spotIds.add(spot.getId()));

        List<Job> pickupSpotJobs = jobRepository.findAllLatestByPickupSpotIds(spotIds);
        Map<Spot, List<Job>> pickupSpotToJobMap =
                pickupSpotJobs.stream().collect(Collectors.groupingBy(Job::getPickupSpot));

        List<SpotExportDto> spotExportDtos = new ArrayList<>();
        spots.forEach(spot -> {
            SpotExportDto spotDto = spotMapper.mapToExportDto(spot, timeZone);
            if (Status.EMPTY.equals(spot.getStatus()) || Status.TO_BE_OCCUPIED.equals(spot.getStatus())) {
                ZonedDateTime sinceEmptyDate = spot.getCreatedDate();
                if (pickupSpotToJobMap.containsKey(spot)
                        && CollectionUtils.isNotEmpty(pickupSpotToJobMap.get(spot))) {
                    Job job = pickupSpotToJobMap.get(spot).get(0);
                    sinceEmptyDate = job.getPickupDateTime();
                }
                Long emptiedSinceSeconds = ChronoUnit.SECONDS.between(sinceEmptyDate, ZonedDateTime.now());
                spotDto.setEmptiedSinceSeconds(emptiedSinceSeconds);
            }
            spotExportDtos.add(spotDto);
        });
        try {

//	    	Workbook workbook = new XSSFWorkbook();
//	        Sheet sheet = workbook.createSheet("Spots");
//	        Row headerRow = sheet.createRow(0);
//	        headerRow.createCell(0).setCellValue("spotId");
//	        headerRow.createCell(1).setCellValue("spotName");
//	        headerRow.createCell(2).setCellValue("locationName");
//	        headerRow.createCell(3).setCellValue("type");
//	        headerRow.createCell(4).setCellValue("status");
//	        headerRow.createCell(5).setCellValue("latitude");
//	        headerRow.createCell(6).setCellValue("longitude");
//	        headerRow.createCell(7).setCellValue("remarks");
//	        headerRow.createCell(8).setCellValue("isActive");
//	        headerRow.createCell(9).setCellValue("emptiedSinceSeconds");
//	        headerRow.createCell(10).setCellValue("fleetCarrier");
//	        headerRow.createCell(11).setCellValue("fleetType");
//	        headerRow.createCell(12).setCellValue("fleetUnitNumber");
//	        headerRow.createCell(13).setCellValue("fleetStatus");
//	        headerRow.createCell(14).setCellValue("createdDate");
//	        headerRow.createCell(15).setCellValue("lastModifiedDate");
//	        headerRow.createCell(16).setCellValue("createdBy");
//	        headerRow.createCell(17).setCellValue("lastModifiedBy");
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Spots");
            Row headerRow = sheet.createRow(0);

            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            boldFont.setColor(IndexedColors.WHITE.getIndex());

            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFont(boldFont);
            cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            // Create cells with bold values
            Cell cell1 = headerRow.createCell(0);
            cell1.setCellValue("Spot Id");
            cell1.setCellStyle(cellStyle);

            Cell cell2 = headerRow.createCell(1);
            cell2.setCellValue("Spot Name");
            cell2.setCellStyle(cellStyle);

            Cell cell3 = headerRow.createCell(2);
            cell3.setCellValue("Location Name");
            cell3.setCellStyle(cellStyle);

            Cell cell4 = headerRow.createCell(3);
            cell4.setCellValue("Type");
            cell4.setCellStyle(cellStyle);

            Cell cell5 = headerRow.createCell(4);
            cell5.setCellValue("Status");
            cell5.setCellStyle(cellStyle);

            Cell cell6 = headerRow.createCell(5);
            cell6.setCellValue("Latitude");
            cell6.setCellStyle(cellStyle);

            Cell cell7 = headerRow.createCell(6);
            cell7.setCellValue("Longitude");
            cell7.setCellStyle(cellStyle);

            Cell cell8 = headerRow.createCell(7);
            cell8.setCellValue("Remarks");
            cell8.setCellStyle(cellStyle);

            Cell cell9 = headerRow.createCell(8);
            cell9.setCellValue("Is Active");
            cell9.setCellStyle(cellStyle);

            Cell cell10 = headerRow.createCell(9);
            cell10.setCellValue("Emptied Since Seconds");
            cell10.setCellStyle(cellStyle);

            Cell cell11 = headerRow.createCell(10);
            cell11.setCellValue("Fleet Carrier");
            cell11.setCellStyle(cellStyle);

            Cell cell12 = headerRow.createCell(11);
            cell12.setCellValue("Fleet Type");
            cell12.setCellStyle(cellStyle);

            Cell cell13 = headerRow.createCell(12);
            cell13.setCellValue("Fleet Unit Number");
            cell13.setCellStyle(cellStyle);

            Cell cell14 = headerRow.createCell(13);
            cell14.setCellValue("Fleet Status");
            cell14.setCellStyle(cellStyle);

            Cell cell15 = headerRow.createCell(14);
            cell15.setCellValue("Created Date");
            cell15.setCellStyle(cellStyle);

            Cell cell16 = headerRow.createCell(15);
            cell16.setCellValue("last Modified Date");
            cell16.setCellStyle(cellStyle);

            Cell cell17 = headerRow.createCell(16);
            cell17.setCellValue("Created By");
            cell17.setCellStyle(cellStyle);

            Cell cell18 = headerRow.createCell(17);
            cell18.setCellValue("Last Modified By");
            cell18.setCellStyle(cellStyle);
            int rowNum = 1;
            for (SpotExportDto spotExportDto : spotExportDtos) {

                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(spotExportDto.getSpotId());
                row.createCell(1).setCellValue(spotExportDto.getSpotName());
                row.createCell(2).setCellValue(spotExportDto.getLocationName());
                row.createCell(3).setCellValue(spotExportDto.getType().toString());
                row.createCell(4).setCellValue(spotExportDto.getStatus().toString());
                row.createCell(5).setCellValue(spotExportDto.getLatitude());
                row.createCell(6).setCellValue(spotExportDto.getLongitude());
                row.createCell(7).setCellValue(spotExportDto.getRemarks());
                if (spotExportDto.getIsActive() == true)
                    row.createCell(8).setCellValue("true");
                else
                    row.createCell(8).setCellValue("false");
                if (spotExportDto.getEmptiedSinceSeconds() != null)
                    row.createCell(9).setCellValue(spotExportDto.getEmptiedSinceSeconds());
                else
                    row.createCell(9).setCellValue("---");
                row.createCell(10).setCellValue(spotExportDto.getFleetCarrier());
                if (spotExportDto.getFleetType() != null)
                    row.createCell(11).setCellValue(spotExportDto.getFleetType().toString());
                else
                    row.createCell(11).setCellValue("---");
                row.createCell(12).setCellValue(spotExportDto.getFleetUnitNumber());
                if (spotExportDto.getFleetStatus() != null && spotExportDto.getFleetStatus().toString() == "FULL") {
                    row.createCell(13).setCellValue("LOADED");
                } else if (spotExportDto.getFleetStatus() != null) {
                    row.createCell(13).setCellValue(spotExportDto.getFleetStatus().toString());
                } else {
                    row.createCell(13).setCellValue("---");
                }

                row.createCell(14).setCellValue(spotExportDto.getCreatedDate());
                row.createCell(15).setCellValue(spotExportDto.getLastModifiedDate());
                row.createCell(16).setCellValue(spotExportDto.getCreatedBy());
                row.createCell(17).setCellValue(spotExportDto.getLastModifiedBy());

            }
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
            try (OutputStream outputStream = new FileOutputStream(fileName)) {
                workbook.write(outputStream);
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.FLEET_EXPORT, e.getMessage());
        }


    }


    @Override
    public PagedResponse<SpotDto> getDropdownSpots(String clientId, Predicate predicate, Pageable pageable,
                                                   String timeZone) {

        Page<Spot> spotsPage = spotRepository.findAll(predicate, pageable);
        return PagedResponse.<SpotDto>builder().list(spotsPage.stream().map(spot -> {
        	  	
        	Status nextJobStatus = null;
	        String nextTrailer = null;
	        Job nextJob = null;   
	        Set<Job> unwantedPickupJobs = new HashSet<>();
	        Set<Job> unwantedDropoffJobs = new HashSet<>();
	        
	        Set<Job> pickUpJobs = spot.getPickUpJobs();
	        if(!pickUpJobs.isEmpty()) {        	       
	        	
	        	for(Job job : pickUpJobs) {
	        		if(Objects.isNull(job.getAssignedAt())) {
	        			unwantedPickupJobs.add(job);
	        		}
	        	}
	        	if(unwantedPickupJobs.size() > 0) {
	        		pickUpJobs.removeAll(unwantedPickupJobs);
	        	}
	        	
	        	for(Job job : pickUpJobs) {
    	           	if(nextJob == null) {
    	           		nextJob = job;
    	           		nextJobStatus = Status.TO_BE_EMPTY;
    	           	}else if(nextJob.getAssignedAt() != null && job.getAssignedAt() != null && nextJob.getAssignedAt().isAfter(job.getAssignedAt())){
    	           		nextJob = job;
    	           		nextJobStatus = Status.TO_BE_EMPTY;
    	           	}
    	        }
	        }
	        
	        Set<Job> dropOffJobs = spot.getDropOffJobs();
	        if(!dropOffJobs.isEmpty()) {
	        	
	        	for(Job job : dropOffJobs) {
	        		if(Objects.isNull(job.getAssignedAt())) {
	        			unwantedDropoffJobs.add(job);
	        		}
	        	}
	        	if(unwantedDropoffJobs.size() > 0) {
	        		dropOffJobs.removeAll(unwantedDropoffJobs);
	        	}
	        	for(Job job : dropOffJobs) {
	        		if(nextJob == null) {
	        			nextJob = job;
	        			nextJobStatus = Status.TO_BE_OCCUPIED;
	        		}else if(nextJob.getCreatedDate() != null && job.getCreatedDate() != null && nextJob.getCreatedDate().isAfter(job.getCreatedDate())) {
	        			nextJob = job;
    	           		nextJobStatus = Status.TO_BE_OCCUPIED;
	        		}
	        	}
	        } 
	        if(nextJob != null) {
	        	nextTrailer = nextJob.getFleet().getUnitNumber();
	        }
	                	        
            SpotDto spotDto = spotMapper.mapToDto(spot, timeZone);
            spotDto.setNextJobStatus(nextJobStatus);
            spotDto.setNextTrailer(nextTrailer);
            return spotDto; 	
         }).collect(Collectors.toList())).page(spotsPage.getNumber()).size(spotsPage.getSize())
         .totalElements(spotsPage.getTotalElements())
         .build();
    }

    @Override
    @Transactional
    public void activateSpot(String clientId, String spotId) {
//	    log.info(">> activateSpot({}, {}, {})", clientId, spotId);
        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
        Spot spot = spotRepository.findByClientAndUuid(client, spotId)
                .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, spotId));
        spot.setIsActive(true);
        spotRepository.save(spot);
    }

    @Override
    public List<FleetStatusCountDto> countOfFleetStatus(String clientId, String locationId) {

        Long emptyCount = spotRepository.countEmptyStatus(clientId, locationId);
        FleetStatusCountDto dto = FleetStatusCountDto.builder()
                .fleetStatus(Job.FleetStatus.EMPTY)
                .count(emptyCount)
                .build();

        Long fullCount = spotRepository.countFullStatus(clientId, locationId);
        FleetStatusCountDto dto1 = FleetStatusCountDto.builder()
                .fleetStatus(FleetStatus.FULL)
                .count(fullCount)
                .build();

        return List.of(dto, dto1);
    }
}
