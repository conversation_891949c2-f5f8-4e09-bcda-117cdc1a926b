package com.ma.spoton.api.services;

import com.itextpdf.awt.DefaultFontMapper;
import com.itextpdf.text.Image;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.JobExportDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.SpotDto;
import com.ma.spoton.api.dtos.SpotExportDto;
import com.ma.spoton.api.dtos.TrailerAuditDto;
import com.ma.spoton.api.dtos.TrailerAuditExportDto;
import com.ma.spoton.api.dtos.TrailerAuditPdfExportDto;
import com.ma.spoton.api.dtos.UserAuthDto;
//import com.ma.spoton.api.entities.Carriers;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.Job.FleetStatus;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.QSpot;
import com.ma.spoton.api.entities.QTrailerAudit;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.entities.TrailerAudit;
import com.ma.spoton.api.entities.Spot.Status;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.SpotMapper;
import com.ma.spoton.api.mappers.TrailerAuditMapper;
//import com.ma.spoton.api.repositories.CarriersRepository;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.FleetRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.SpotRepository;
import com.ma.spoton.api.repositories.TrailerAuditRepository;
import com.ma.spoton.api.requests.TrailerAuditRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.utils.CSVUtils;
import com.ma.spoton.api.utils.DateTimeUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.apache.poi.util.Units;
import org.apache.poi.xddf.usermodel.chart.ChartTypes;
import org.apache.poi.xddf.usermodel.chart.LegendPosition;
import org.apache.poi.xddf.usermodel.chart.XDDFChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFChartLegend;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSourcesFactory;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.ClientAnchor.AnchorType;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFChart;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFTextBox;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtilities;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.labels.PieSectionLabelGenerator;
import org.jfree.chart.labels.StandardPieSectionLabelGenerator;
import org.jfree.chart.plot.PieLabelLinkStyle;
import org.jfree.chart.plot.PiePlot;
import org.jfree.chart.title.TextTitle;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.data.general.PieDataset;
import org.jfree.ui.RectangleEdge;
import org.jfree.ui.RectangleInsets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.awt.Color;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.AttributedString;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;

@Slf4j
@Service
public class TrailerAuditServiceImpl implements TrailerAuditService {
	
	@Autowired
	private SpotRepository spotRepository;
	
	@Autowired
	private FleetRepository fleetReository;
	
	@Autowired
    private ClientRepository clientRepository;
	
	@Autowired
    private JobRepository jobRepository;
	
	@Autowired
    private SpotMapper spotMapper;
	
	@Transactional
	public void createTrailerAudit(TrailerAuditRequest[] trailerAuditRequests) {
		
		for(TrailerAuditRequest trailerAuditRequest : trailerAuditRequests) {
			Spot spot = spotRepository.findActiveByUuid(trailerAuditRequest.getSpotId())
		      		  .orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, trailerAuditRequest.getSpotId()));
			if(Objects.nonNull(trailerAuditRequest.getFleetId())) {
				
				Fleet fleet = fleetReository.findActiveByUuid(trailerAuditRequest.getFleetId())
						.orElseThrow(() -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, trailerAuditRequest.getFleetId()));
				fleet.setSpot(spot);
				fleetReository.save(fleet);
				
				if(Objects.nonNull(spot.getFleet())) {
					
					if(!spot.getFleet().equals(fleet)) {
						spot.setLastOccupiedTime(ZonedDateTime.now());
					}
					
				}else {
					spot.setLastOccupiedTime(ZonedDateTime.now());
				}
				
				List<Spot> spots = spotRepository.findAllByFleet(fleet); 
				for (Spot spotUnique : spots) {
					spotUnique.setFleet(null);
				}
				spotRepository.saveAll(spots);
				
				spot.setFleet(fleet);
				spot.setStatus(Status.OCCUPIED);
				
				
				
			}
		    if(Objects.nonNull(trailerAuditRequest.getTrailerStatus())) {
		    	spot.getFleet().setFleetStatus(trailerAuditRequest.getTrailerStatus());
		    }
		    if(Objects.nonNull(trailerAuditRequest.getNotes())) {
		    	spot.setNotes(trailerAuditRequest.getNotes());
		    }
			spotRepository.save(spot);
		}
	}

	@Override
	@Transactional
	public void clearSpot(String spotId) {
		
		Spot spot = spotRepository.findActiveByUuid(spotId)
				.orElseThrow(() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, spotId));
		
		if(Objects.nonNull(spot.getFleet())) {
			
			Fleet fleet = fleetReository.findActiveByUuid(spot.getFleet().getUuid())
					.orElseThrow(() -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, spot.getFleet().getUuid()));
			fleet.setSpot(null);
			fleetReository.save(fleet);
		}
		
		spot.setFleet(null);
		spot.setNotes(null);
		if(!spot.getStatus().equals(Status.EMPTY)) {
			
			spot.setStatus(Status.EMPTY);
			spot.setLastEmptiedTime(ZonedDateTime.now());
		}
		
		spotRepository.save(spot);
				
	}
	
	
	
	@Override
    @org.springframework.transaction.annotation.Transactional(readOnly = true)
    public Resource exportTrailerAuditAsEXCEL(String clientId,String locationIds, FleetStatus fleetStatus, Predicate predicate, String timeZone) {
        // TODO Auto-generated method stub
		 
		
		BaseColor[] baseColors = null;
		Color[] colors = null;
		Color[] grayscaleColors = {
				
				new Color(183, 183, 183),
				new Color(217, 217, 217),
				new Color(102, 102, 102),
		};
		
		
        Client client = clientRepository.findActiveByUuid(clientId)
                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));    
               
        BooleanExpression mandatoryPredicate = QSpot.spot.location.client.eq(client);
        if (Objects.isNull(predicate)) {
            predicate = mandatoryPredicate;
        } else {
            predicate = mandatoryPredicate.and(predicate);
        }
        
        if(locationIds != null) {
        	
            List<String> requiredLocationIds = Arrays.asList(locationIds.split(","));
            BooleanExpression locationPredicate = QSpot.spot.location.uuid.in(requiredLocationIds);
            if (Objects.isNull(predicate)) {
                predicate = locationPredicate;
            } else {
                predicate = locationPredicate.and(predicate);
            }
        }else {
        	
        	BooleanExpression locationPredicate = QSpot.spot.location.isActive.eq(true);
        	if (Objects.isNull(predicate)) {
                predicate = locationPredicate;
            } else {
                predicate = locationPredicate.and(predicate);
            }
        }
   
        if(fleetStatus != null) {
        	 
        	BooleanExpression fleetStatusPredicate = QSpot.spot.fleet.fleetStatus.eq(fleetStatus);
        	if (Objects.isNull(predicate)) {
                predicate = fleetStatusPredicate;
            } else {
                predicate = fleetStatusPredicate.and(predicate);
            }
        }
        var spots = spotRepository.findAll(predicate);
   
        List<SpotExportDto> spotExportDtos = new ArrayList<>();
        List<SpotExportDto> occupiedSpotExportDtos = new ArrayList<>();
        
        spots.forEach(spot -> {
            SpotExportDto spotDto = spotMapper.mapToExportDto(spot, timeZone);
            spotExportDtos.add(spotDto);
        });
        
        Set<Location> uniqueLocations = new HashSet<>();
        spots.forEach(spot -> {
        	
        	if(spot.getFleet() != null) {
        		uniqueLocations.add(spot.getLocation());
        	}    
        });
        
        for(SpotExportDto spotExportDto : spotExportDtos) {
        	if(spotExportDto.getFleetUnitNumber() != null) {
        		occupiedSpotExportDtos.add(spotExportDto);
        	}
        }
        
        Set<String> uniqueLocationNames = occupiedSpotExportDtos.stream().map(SpotExportDto::getLocationName).collect(Collectors.toSet());
        Set<String> uniqueStatuses = occupiedSpotExportDtos.stream().filter(dto -> dto.getFleetStatus() != null).map(dto -> dto.getFleetStatus().toString()).collect(Collectors.toSet());
    
        Map<String, Long> totalEntriesInArea = getCountOfProperty(uniqueLocationNames, occupiedSpotExportDtos, SpotExportDto::getLocationName);
        Map<String, Long> totalEntriesWithStatus = getCountOfProperty(uniqueStatuses, occupiedSpotExportDtos.stream().filter(dto -> dto.getFleetStatus() != null).collect(Collectors.toList()), dto -> dto.getFleetStatus().toString());
        
		Map<String, Color> areaColorForPieChart = new HashMap<>();
		Map<String, BaseColor> areaColorForTable = new HashMap<>();
		
		int i = 0;
		for(Location location: uniqueLocations)
		{
			if(location!=null)
			{
				if(location.getPieChartColor()!=null && location.getPieChartColor().length()>0)
				{ 
//					log.info("location1>>>>>({})",location.getLocationName());
					int[] rgb = hexToRGB(location.getPieChartColor());
					areaColorForPieChart.put(location.getLocationName(), new Color(rgb[0],rgb[1],rgb[2]));
					areaColorForTable.put(location.getLocationName(),new BaseColor(rgb[0], rgb[1], rgb[2]));
				}	
				else
				{
//					log.info("location2>>>>>>({})",location.getLocationName());
					int[] defaultRgb = hexToRGB("#edf1f5");
				    areaColorForPieChart.put(location.getLocationName(), new Color(defaultRgb[0], defaultRgb[1], defaultRgb[2]));
				    areaColorForTable.put(location.getLocationName(), new BaseColor(defaultRgb[0], defaultRgb[1], defaultRgb[2]));
				}
			}	
		}
		     
        try {

            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Spots");
            
         // Create title row and merge cells
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
  //          titleCell.setCellValue(formattedDate);
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setFontHeightInPoints((short) 16);
            titleFont.setBold(true);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleCell.setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(0, 7, 0, 5));
            sheet.addMergedRegion(new CellRangeAddress(9, 29, 0, 5));
            
         // Set custom column widths
		    sheet.setColumnWidth(0, 8000); // Column 0 width set to 4000 units
		    sheet.setColumnWidth(1, 6000); // Column 1 width set to 5000 units
		    sheet.setColumnWidth(2, 3800); // Column 2 width set to 6000 units
		    sheet.setColumnWidth(3, 4000); // Column 3 width set to 7000 units
		    sheet.setColumnWidth(4, 3800); // Column 4 width set to 8000 units
		    sheet.setColumnWidth(5, 7800); // Column 5 width set to 9000 units
//		    sheet.setColumnWidth(6, 7000); // Column 6 width set to 10000 units
		    
		    JFreeChart areaPieChart = plotPropertyChart2(uniqueLocationNames, totalEntriesInArea, areaColorForPieChart,10);
			 // Convert chart to image
		    ByteArrayOutputStream chartOutputStream = new ByteArrayOutputStream();
		    ChartUtilities.writeChartAsPNG(chartOutputStream, areaPieChart, 640, 480);
		    int pictureIdx1 = workbook.addPicture(chartOutputStream.toByteArray(), Workbook.PICTURE_TYPE_PNG);
		    chartOutputStream.close();
		    
		    // Create the drawing patriarch
            Drawing<?> drawing1 = sheet.createDrawingPatriarch();
            
         // Create an anchor to position the image
            CreationHelper helper1 = workbook.getCreationHelper();
            ClientAnchor anchor1 = helper1.createClientAnchor();
            anchor1.setCol1(0); // Column B
            anchor1.setRow1(9); // Row 2
            anchor1.setDx1(0 * Units.EMU_PER_PIXEL);
            anchor1.setDy1(15 * Units.EMU_PER_PIXEL);
            anchor1.setDx2(255 * Units.EMU_PER_PIXEL); // Width (1023 is the maximum for x)
            anchor1.setDy2(450 * Units.EMU_PER_PIXEL);
            
         // Create a picture and associate it with the anchor
            Picture picture1 = drawing1.createPicture(anchor1, pictureIdx1);
            picture1.resize(1); 
            
            JFreeChart statusPieChart = plotPropertyChart(uniqueStatuses, totalEntriesWithStatus, grayscaleColors, 10); 
            // Convert chart to image
	        ByteArrayOutputStream chartOutputStream1 = new ByteArrayOutputStream();
	        ChartUtilities.writeChartAsPNG(chartOutputStream1, statusPieChart, 640, 480);
	        int pictureIdx2 = workbook.addPicture(chartOutputStream1.toByteArray(), Workbook.PICTURE_TYPE_PNG);
	        chartOutputStream1.close();
		    
	        // Create the drawing patriarch
            Drawing<?> drawing2 = sheet.createDrawingPatriarch();

            // Create an anchor to position the image
            CreationHelper helper2 = workbook.getCreationHelper();
            ClientAnchor anchor2 = helper2.createClientAnchor();
            anchor2.setCol1(2); // Column B
            anchor2.setRow1(9); // Row 2
            anchor2.setDx1(85 * Units.EMU_PER_PIXEL);
            anchor2.setDy1(15 * Units.EMU_PER_PIXEL);
            anchor2.setDx2(431 * Units.EMU_PER_PIXEL); // Width (1023 is the maximum for x)
            anchor2.setDy2(450 * Units.EMU_PER_PIXEL);
            // Create a picture and associate it with the anchor
            Picture picture2 = drawing2.createPicture(anchor2, pictureIdx2);
            picture2.resize(1); 
            
            // Insert image
            String logoPath2;
			if (client.getClientName().equalsIgnoreCase("Gatorade Dallas")) {
				logoPath2 = "trailer-audit-pdf-images/Excel logo.png";
			} else {
				logoPath2 = "trailer-audit-pdf-images/Excel_logo_ablair.png";
			}
			
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(logoPath2);
			
            if (inputStream == null) {
                throw new FileNotFoundException("Image file not found in resources: trailer-audit-pdf-images/image.png");
            }
            byte[] bytes = IOUtils.toByteArray(inputStream);
            int pictureIdx = workbook.addPicture(bytes, Workbook.PICTURE_TYPE_PNG);
            inputStream.close();
            
            CreationHelper helper = workbook.getCreationHelper();
            Drawing drawing = sheet.createDrawingPatriarch();
            ClientAnchor anchor = helper.createClientAnchor();
            anchor.setAnchorType(AnchorType.MOVE_AND_RESIZE);
//          anchor.setCol1(0); // Column 0
//          anchor.setRow1(1); // Row 0
            anchor.setDx1(0 * Units.EMU_PER_PIXEL);
            anchor.setDy1(0 * Units.EMU_PER_PIXEL);
            anchor.setDx2(700 * Units.EMU_PER_PIXEL); // Width (1023 is the maximum for x)
            anchor.setDy2(110 * Units.EMU_PER_PIXEL);  // Height (255 is the maximum for y)
            
//          anchor.setCol2(1); // Column 1 (Image will span between col1 and col2)
//          anchor.setRow2(4); // Row 1 (Image will span between row1 and row3)
          Picture pict = drawing.createPicture(anchor, pictureIdx);
          pict.resize(1);
            
          Row borderRow1 = sheet.createRow(8);
		  CellStyle borderCellStyle = workbook.createCellStyle();
		  borderCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		  borderCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND); 
		  for(int j=0;j<6;j++)
		  {
				addBorderColorInCell(borderRow1, j, borderCellStyle);
		  }
			borderRow1.setHeightInPoints(7);
		  
			Row borderRow2 = sheet.createRow(33);
			CellStyle borderCellStyle1 = workbook.createCellStyle();
			borderCellStyle1.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
			borderCellStyle1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			
			for(int j=0;j<6;j++)
			{
				addBorderColorInCell(borderRow2, j, borderCellStyle1);
			}
			borderRow2.setHeightInPoints(7);
			
			Row borderRow3 = sheet.createRow(35);
			CellStyle borderCellStyle2 = workbook.createCellStyle();
			borderCellStyle2.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
			borderCellStyle2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			
			for(int j=0;j<6;j++)
			{
				addBorderColorInCell(borderRow3, j, borderCellStyle2);
			}
			borderRow3.setHeightInPoints(7);
          		  
            Row headerRow = sheet.createRow(34);
            headerRow.setHeightInPoints(25);
            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            boldFont.setFontHeightInPoints((short) 10); // Set the font size to 10 points
                  
            boldFont.setColor(IndexedColors.BLACK.getIndex());
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFont(boldFont);
                       
            addHeadingInCell(headerRow, 0, "Dock", cellStyle);
			addHeadingInCell(headerRow, 1, "Location", cellStyle);
			addHeadingInCell(headerRow, 2, "Trailer", cellStyle);
			addHeadingInCell(headerRow, 3, "Trailer Status", cellStyle);
			addHeadingInCell(headerRow, 4, "Notes", cellStyle);
			addHeadingInCell(headerRow, 5, "Last Updated", cellStyle);
			
			
			 byte[] rgb1 = new byte[]{(byte) 255, (byte) 165, (byte) 0};          
	          XSSFCellStyle borderCellStyle3 = (XSSFCellStyle) workbook.createCellStyle();
	          XSSFColor myColor1 = new XSSFColor(rgb1, new DefaultIndexedColorMap());
			
//				CellStyle borderCellStyle1 = workbook.createCellStyle();
				borderCellStyle3.setFillForegroundColor(myColor1);
				borderCellStyle3.setFillPattern(FillPatternType.SOLID_FOREGROUND);
				
				
            
            int rowNum = 36;
            for (SpotExportDto spotExportDto : spotExportDtos) {

            	byte[] color = new byte[]{(byte) 255, (byte) 255, (byte) 255};
            	if(spotExportDto != null && spotExportDto.getLocation() != null) {
					if(spotExportDto.getLocation().getPieChartColor().length() > 0)
					{
						color = hexToRGBbytes(spotExportDto.getLocation().getPieChartColor());
					}else {
						color = hexToRGBbytes("#edf1f5");
					}
					XSSFCellStyle cellStyle1 = (XSSFCellStyle) workbook.createCellStyle();
			        XSSFColor myColor = new XSSFColor(color, new DefaultIndexedColorMap());
			        cellStyle1.setFillForegroundColor(myColor);
			        cellStyle1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			        
			        
			        
                Row row = sheet.createRow(rowNum++);
                Cell cell0 = row.createCell(0);
                cell0.setCellValue(spotExportDto.getSpotName());
                cell0.setCellStyle(cellStyle1);
                
//                row.createCell(0).setCellValue(spotExportDto.getSpotName());
                row.createCell(1).setCellValue(spotExportDto.getLocationName());
                row.createCell(2).setCellValue(spotExportDto.getFleetUnitNumber());
                if(spotExportDto.getFleetStatus() != null) {
                	if(spotExportDto.getFleetStatus().equals(fleetStatus.EMPTY)) {
                		row.createCell(3).setCellValue("EMPTY");
                	}
                    if(spotExportDto.getFleetStatus().equals(fleetStatus.FULL)) {
                    	row.createCell(3).setCellValue("LOADED");
                	}
                }
                row.createCell(4).setCellValue(spotExportDto.getNotes());
                row.createCell(5).setCellValue(spotExportDto.getLastModifiedDate()); 
            	}
            }
            Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
            File tmpFolder = tmpDirPath.toFile();
            String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
            try (OutputStream outputStream = new FileOutputStream(fileName)) {
                workbook.write(outputStream);
                return new InputStreamResource(new FileInputStream(fileName));
            }
        } catch (Exception e) {
            log.error("Error occurred while exporting Excel!", e);
            throw new ServiceException(ErrorCode.FLEET_EXPORT, e.getMessage());
        }
    }

	@Override
	public Resource exportTrailerAuditAsPdf(String clientId, String locationIds, FleetStatus fleetStatus,
			Predicate predicate, String timeZone) {
		
		
//		BaseColor[] baseColors = {new BaseColor(244,184,17), new BaseColor(43,194,194), new BaseColor(76,144,186), new BaseColor(43, 194, 112), new BaseColor(222, 102, 62), new BaseColor(160, 53, 186)};
//		Color[] colors = {new Color(244,184,17), new Color(43,194,194), new Color(76,144,186),new Color(43, 194, 112),new Color(222, 102, 62),new Color(160, 53, 186) };
		
		BaseColor[] baseColors = null;
		Color[] colors = null;
		Color[] grayscaleColors = {
				
				new Color(183, 183, 183),
				new Color(217, 217, 217),
				new Color(102, 102, 102),
		};	
		
		
		float[] columnWidths = { 100f, 100f, 100f, 85f, 85f, 80f};
		PdfWriter writer;
		
		 Client client = clientRepository.findActiveByUuid(clientId)
	                .orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));    
	               
	        BooleanExpression mandatoryPredicate = QSpot.spot.location.client.eq(client);
	        if (Objects.isNull(predicate)) {
	            predicate = mandatoryPredicate;
	        } else {
	            predicate = mandatoryPredicate.and(predicate);
	        }
	        
	        if(locationIds != null) {
	        	
	            List<String> requiredLocationIds = Arrays.asList(locationIds.split(","));
	            BooleanExpression locationPredicate = QSpot.spot.location.uuid.in(requiredLocationIds);
	            if (Objects.isNull(predicate)) {
	                predicate = locationPredicate;
	            } else {
	                predicate = locationPredicate.and(predicate);
	            }
	        }else {
	        	
	        	BooleanExpression locationPredicate = QSpot.spot.location.isActive.eq(true);
	        	if (Objects.isNull(predicate)) {
	                predicate = locationPredicate;
	            } else {
	                predicate = locationPredicate.and(predicate);
	            }
	        }
	   
	        if(fleetStatus != null) {
	        	 
	        	BooleanExpression fleetStatusPredicate = QSpot.spot.fleet.fleetStatus.eq(fleetStatus);
	        	if (Objects.isNull(predicate)) {
	                predicate = fleetStatusPredicate;
	            } else {
	                predicate = fleetStatusPredicate.and(predicate);
	            }
	        }
	        var spots = spotRepository.findAll(predicate);
	   
	        List<SpotExportDto> spotExportDtos = new ArrayList<>();
	        List<SpotExportDto> occupiedSpotExportDtos = new ArrayList<>();
	        
	        spots.forEach(spot -> {
	            SpotExportDto spotDto = spotMapper.mapToExportDto(spot, timeZone);
	            spotExportDtos.add(spotDto);
	        });
	        
	        
	        
//	        for(SpotExportDto spotExportDto : spotExportDtos) {
//	        	if(spotExportDto.getFleetStatus() == null) {
//	        		spotExportDto.setFleetStatus(FleetStatus.NIL);
//	        	}
//	        }
	        
	        
	        Set<Location> uniqueLocations = new HashSet<>();
	        spots.forEach(spot -> {
	        	
	        	if(spot.getFleet() != null) {
	        		uniqueLocations.add(spot.getLocation());
	        	}    
	        });
	        
	        for(SpotExportDto spotExportDto : spotExportDtos) {
	        	if(spotExportDto.getFleetUnitNumber() != null) {
	        		occupiedSpotExportDtos.add(spotExportDto);
	        	}
	        }
	        
	        Set<String> uniqueLocationNames = occupiedSpotExportDtos.stream().map(SpotExportDto::getLocationName).collect(Collectors.toSet());
	        Set<String> uniqueStatuses = occupiedSpotExportDtos.stream().filter(dto -> dto.getFleetStatus() != null).map(dto -> dto.getFleetStatus().toString()).collect(Collectors.toSet());
        
	        Map<String, Long> totalEntriesInArea = getCountOfProperty(uniqueLocationNames, occupiedSpotExportDtos, SpotExportDto::getLocationName);
			Map<String, Long> totalEntriesWithStatus = getCountOfProperty(uniqueStatuses, occupiedSpotExportDtos.stream().filter(dto -> dto.getFleetStatus() != null).collect(Collectors.toList()), dto -> dto.getFleetStatus().toString());
	        
			Map<String, Color> areaColorForPieChart = new HashMap<>();
			Map<String, BaseColor> areaColorForTable = new HashMap<>();
			
			int i = 0;
			for(Location location: uniqueLocations)
			{
				if(location!=null)
				{
					if(location.getPieChartColor()!=null && location.getPieChartColor().length()>0)
					{ 
//						log.info("location1>>>>>({})",location.getLocationName());
						int[] rgb = hexToRGB(location.getPieChartColor());
						areaColorForPieChart.put(location.getLocationName(), new Color(rgb[0],rgb[1],rgb[2]));
						areaColorForTable.put(location.getLocationName(),new BaseColor(rgb[0], rgb[1], rgb[2]));
					}	
					else
					{
//						log.info("location2>>>>>>({})",location.getLocationName());
						 int[] defaultRgb = hexToRGB("#edf1f5");
						 areaColorForPieChart.put(location.getLocationName(), new Color(defaultRgb[0], defaultRgb[1], defaultRgb[2]));
						 areaColorForTable.put(location.getLocationName(), new BaseColor(defaultRgb[0], defaultRgb[1], defaultRgb[2]));
					}
				}	
			}
									
	        try {

				Document document = new Document();
				writer = PdfWriter.getInstance(document, new FileOutputStream("iTextTable.pdf"));
				document.open();
				PdfContentByte pdfContentByte = writer.getDirectContent();
				
				
				JFreeChart areaPiChart = plotPropertyChart2(uniqueLocationNames, totalEntriesInArea, areaColorForPieChart,5);
				plotChartInPdfAtGivenAxis(pdfContentByte, areaPiChart, 50, 500);
				
				JFreeChart statusPiChart = plotPropertyChart(uniqueStatuses, totalEntriesWithStatus, grayscaleColors,5);
				plotChartInPdfAtGivenAxis(pdfContentByte, statusPiChart, 300, 500);			
				
				uploadTrailerAuditPdfLogo(document, "classpath:trailer-audit-pdf-images/Picture3.png", 220f, 729f, 130f, 35f);	
				String latoFontPath = "/Font/Lato-Regular.ttf";
				java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
				if (fontStream == null) {
					throw new IOException("Font file not found");
				}
				BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
				com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 10, com.itextpdf.text.Font.NORMAL, BaseColor.GRAY);
				com.itextpdf.text.Font blackFont = new com.itextpdf.text.Font(bf, 8, com.itextpdf.text.Font.NORMAL, BaseColor.BLACK);
				
				 // Drawing a gray horizontal line
	            pdfContentByte.setColorStroke(new BaseColor(205, 205, 205));
	            pdfContentByte.setLineWidth(4f); // Set line width as needed
	            pdfContentByte.moveTo(50, 710); // Starting point (x, y)
	            pdfContentByte.lineTo(550, 710); // Ending point (x, y)
	            pdfContentByte.stroke();
	            
	         // Drawing a gray horizontal line
	            pdfContentByte.setColorStroke(new BaseColor(205, 205, 205));
	            pdfContentByte.setLineWidth(4f); // Set line width as needed
	            pdfContentByte.moveTo(50, 489); // Starting point (x, y)
	            pdfContentByte.lineTo(550, 489); // Ending point (x, y)
	            pdfContentByte.stroke();
	            
	         // Drawing a gray vertical line
	            pdfContentByte.setColorStroke(new BaseColor(205, 205, 205));
	            pdfContentByte.setLineWidth(1f); // Set line width as needed
	            pdfContentByte.moveTo(50,710); // Starting point (x, y)
	            pdfContentByte.lineTo(50,490); // Ending point (x, y)
	            pdfContentByte.stroke();
	            
	         // Drawing a gray vertical line
	            pdfContentByte.setColorStroke(new BaseColor(205, 205, 205));
	            pdfContentByte.setLineWidth(1f); // Set line width as needed
	            pdfContentByte.moveTo(550,710); // Starting point (x, y)
	            pdfContentByte.lineTo(550,490); // Ending point (x, y)
	            pdfContentByte.stroke();
	            
	            ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER, new Phrase("Trailers by Location", font), 103, 683, 0);
				ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER, new Phrase("Trailer Status", font), 341, 683, 0);
				
				//uploadTrailerAuditPdfLogo(document, "classpath:trailer-audit-pdf-images/Pdflogo.png",40f, 729f, 500f, 70f);
				String logoPath;
				if (client.getClientName().equalsIgnoreCase("Gatorade Dallas")) {
					logoPath = "classpath:trailer-audit-pdf-images/Pdflogo.png";
				} else {
				    logoPath = "classpath:trailer-audit-pdf-images/Pdflogo_ablair.png";
				}
				uploadTrailerAuditPdfLogo(document, logoPath, 40f, 729f, 500f, 70f);

				
				if (spotExportDtos.size() > 60) {
					
					List<SpotExportDto> firstPageEntries = spotExportDtos.subList(0, 35);
					List<SpotExportDto> remainingPageEntries = spotExportDtos.subList(36, spotExportDtos.size());
					
					PdfPTable table = modifyTableInPdf(columnWidths, firstPageEntries, areaColorForTable, true);
					table.writeSelectedRows(0, -1, 50, 470, writer.getDirectContent());

					List<List<SpotExportDto>> paginatedRows = paginate(remainingPageEntries, 74);
					for (List<SpotExportDto> pageRows : paginatedRows) {
						document.newPage();
						PdfPTable nextTable = modifyTableInPdf(columnWidths, pageRows, areaColorForTable, false);
						document.add(nextTable);

					}
				} else {
					PdfPTable table = modifyTableInPdf(columnWidths, spotExportDtos, areaColorForTable, true);
					table.writeSelectedRows(0, -1, 50, 470, writer.getDirectContent());
				}

				document.close();
				File pdfFile = new File("iTextTable.pdf");
				return new InputStreamResource(new FileInputStream(pdfFile));

			} catch (Exception e) {
				log.error("Error occurred while exporting Excel!", e);
				throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
			}
	}
	
	
	
	private PdfPTable modifyTableInPdf(float[] columnWidths, List<SpotExportDto> spotExportData, Map<String, BaseColor> areaColorForTable, Boolean isFirstPage) {
		PdfPTable table = new PdfPTable(columnWidths);
		if (isFirstPage == true) {
			addTableHeader(table);
		}
		addRows(table, spotExportData, areaColorForTable);
		table.setTotalWidth(500);

		table.setLockedWidth(true);

		return table;
	}

	private void addTableHeader(PdfPTable table) {
		try {
			String latoFontPath = "/Font/Lato-SemiBold.ttf";
			java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
			if (fontStream == null) {
				throw new IOException("File not found");
			}
			BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED,
					fontStream.readAllBytes(), null);
			com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL,
					BaseColor.BLACK);
			Stream.of("DOCK", "LOCATION","TRAILER", "TRAILER STATUS", "NOTES", "LAST UPDATED")
					.forEach(columnTitle -> {
						PdfPCell header = new PdfPCell();
						header.setBorderColor(new BaseColor(221, 221, 223));
						header.setPhrase(new Phrase(columnTitle, font));
						header.setHorizontalAlignment(Element.ALIGN_LEFT);
						header.setVerticalAlignment(Element.ALIGN_MIDDLE);
						header.setFixedHeight(20f);
						table.addCell(header);
					});

			for (int i = 0; i < 6; i++) {
				PdfPCell borderCell = new PdfPCell();
				borderCell.setBackgroundColor(new BaseColor(205, 205, 205));
				borderCell.setBorderColor(new BaseColor(221, 221, 223));
				borderCell.setFixedHeight(5);
				table.addCell(borderCell);
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
	}

	private void addRows(PdfPTable table, List<SpotExportDto> spotExportDtos, Map<String, BaseColor> areaColorForTable) {
		try {
			for (SpotExportDto spotExportDto : spotExportDtos) {
				
				BaseColor color = areaColorForTable.get(spotExportDto.getLocationName());
				String latoFontPath = "/Font/Lato-Regular.ttf";
				java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
				if (fontStream == null) {
					// throw new IOException("Font file not found");
					throw new IOException("File not found");
				}
				BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED,
						BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
				com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL);
//				addCellInPDFWithHeading(table, jobExportDto.getJobNumber(), font, BaseColor.WHITE,
//						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, spotExportDto.getSpotName(), font, color,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, spotExportDto.getLocationName(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, spotExportDto.getFleetUnitNumber(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				if(spotExportDto.getFleetStatus() != null) {
					if(spotExportDto.getFleetStatus().equals(FleetStatus.EMPTY)) {
						addCellInPDFWithHeading(table, "EMPTY", font, BaseColor.WHITE,
								new BaseColor(221, 221, 223));
					}
                    if(spotExportDto.getFleetStatus().equals(FleetStatus.FULL)) {
                    	addCellInPDFWithHeading(table, "LOADED", font, BaseColor.WHITE,
    							new BaseColor(221, 221, 223));
					}
                    if(spotExportDto.getFleetStatus().equals(FleetStatus.NIL)) {
                    	addCellInPDFWithHeading(table, "NIL", font, BaseColor.WHITE,
    							new BaseColor(221, 221, 223));
                    }
				}else {
					
					addCellInPDFWithHeading(table, null, font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}
				
				addCellInPDFWithHeading(table, spotExportDto.getNotes(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, spotExportDto.getLastModifiedDate(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));

			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
	}

	private static void addCellInPDFWithHeading(PdfPTable table, String heading, com.itextpdf.text.Font font,
			BaseColor backgroundColor, BaseColor borderColor) {
		PdfPCell cell = new PdfPCell(new Phrase(heading, font));
		cell.setBackgroundColor(backgroundColor);
		cell.setBorderColor(borderColor);
		cell.setFixedHeight(10f);
		cell.setNoWrap(false);
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(cell);
	}

	private List<List<SpotExportDto>> paginate(List<SpotExportDto> rows, int rowsPerPage) {

		return Stream.iterate(0, i -> i + rowsPerPage).limit((long) Math.ceil((double) rows.size() / rowsPerPage))
				.map(i -> rows.subList(i, Math.min(i + rowsPerPage, rows.size()))).collect(Collectors.toList());
	}

	private void uploadTrailerAuditPdfLogo(Document document, String fileName, float imageHorizontalPosition,
			float imageVerticalPosition, float imageWidth, float imageHeight) throws IOException, DocumentException {
		Image image = Image.getInstance(fileName);
		image.setAbsolutePosition(imageHorizontalPosition, imageVerticalPosition);
		image.scaleAbsoluteWidth(imageWidth);
		image.scaleAbsoluteHeight(imageHeight);
		document.add(image);
	}
	
	public static byte[] hexToRGBbytes(String hexColor) {
	    // Remove the leading # if it's there
	    if (hexColor.startsWith("#")) {
	        hexColor = hexColor.substring(1);
	    }
	     if (hexColor.endsWith("-HEX") || hexColor.endsWith("-hex")) {
	        hexColor = hexColor.substring(0, 6);
	    }
	    // Parse the hex string to integer
	    int hexValue = Integer.parseInt(hexColor, 16);

	    // Extract RGB components and convert them to byte
	    byte red = (byte) ((hexValue >> 16) & 0xFF);
	    byte green = (byte) ((hexValue >> 8) & 0xFF);
	    byte blue = (byte) (hexValue & 0xFF);
	    return new byte[]{red, green, blue};
	}
	
	
    public static int[] hexToRGB(String hexColor) {
        // Remove the leading # if it's there
        if (hexColor.startsWith("#")) {
            hexColor = hexColor.substring(1);
        }	
         if(hexColor.endsWith("-HEX") || hexColor.endsWith("-hex"))
        {
        	hexColor = hexColor.substring(0,6);
        }
         // Parse the hex string to integer
        int hexValue = Integer.parseInt(hexColor, 16);
        
         // Extract RGB components
        int red = (hexValue >> 16) & 0xFF;
        int green = (hexValue >> 8) & 0xFF;
        int blue = hexValue & 0xFF;
        
        return new int[]{red, green, blue};
    }


    static class CustomPieSectionLabelGenerator implements PieSectionLabelGenerator {

      @Override
	  public String generateSectionLabel(PieDataset dataset, Comparable key) {
		double value = dataset.getValue(key).doubleValue();
		double total = 0.0;
		for (int i = 0; i < dataset.getItemCount(); i++) {
			total += dataset.getValue(i).doubleValue();
		}
		double percentage = (value / total) * 100;
		return String.format("%s %.0f (%.2f%%)", key, value, percentage);
	 }
	
	 @Override
	 public AttributedString generateAttributedSectionLabel(PieDataset dataset, Comparable key) {
	   	return null;
	 }
  }
    
    private Map<String, Long> getCountOfProperty(Set<String> elements, List<SpotExportDto> spotExportData,
			 Function<SpotExportDto, String> propertyExtractor) {
        return elements.stream()
              .collect(Collectors.toMap(
                  Function.identity(),
                  property -> spotExportData.stream()
                        .filter(dto -> propertyExtractor.apply(dto).equals(property))
                        .count()));
    }
    
    
    private JFreeChart plotPropertyChart2(Set<String> elements, Map<String, Long> entries, Map<String, Color> areaColorForPieChart, int fontSize) {
		DefaultPieDataset defaultCategoryDataset = new DefaultPieDataset();
		for (String entry : elements) {
			defaultCategoryDataset.setValue(entry, entries.get(entry));
		}
    
		JFreeChart pieChart = ChartFactory.createPieChart("", defaultCategoryDataset, false, false, false);

		PiePlot plot = (PiePlot) pieChart.getPlot();
		plot.setBackgroundPaint(Color.WHITE);
		plot.setOutlinePaint(new Color(73, 73, 73));
		plot.setShadowPaint(Color.WHITE);
		plot.setShadowPaint(null);
		plot.setSimpleLabels(false);
		plot.setOutlineStroke(new BasicStroke(0.3f));
		
		for (String area : elements) {

			plot.setSectionPaint(area, areaColorForPieChart.get(area));
			plot.setLabelFont(new java.awt.Font("SansSerif", java.awt.Font.PLAIN, fontSize));
//        	 plot.setLabelGenerator(new CustomPieSectionLabelGenerator("{0} ({2})"));
			plot.setLabelGenerator(new CustomPieSectionLabelGenerator());
    
    

//			plot.setLabelGenerator(new StandardPieSectionLabelGenerator("{0} ({2})"));

			plot.setLabelBackgroundPaint(Color.WHITE);
			plot.setLabelOutlinePaint(Color.WHITE);
			plot.setLabelOutlineStroke(null);
//			plot.setShadowPaint(Color.WHITE);
			plot.setLabelBackgroundPaint(Color.WHITE);
			plot.setShadowPaint(null);  // Remove shadow paint from the plot
	        plot.setLabelShadowPaint(null);
	        plot.setLabelPaint(Color.GRAY);

//			plot.setSimpleLabels(false);
//			plot.setLabelLinkPaint(Color.BLACK);
//			plot.setLabelLinkStroke(new BasicStroke(1.0f));
			
		        

		}
		
		plot.setLabelLinkStyle(PieLabelLinkStyle.STANDARD); // Ensure lines are straight
		 plot.setLabelLinkPaint(Color.GRAY); // Set the color for the label lines
		 plot.setLabelLinkStroke(new BasicStroke(1.0f)); // Set the thickness of the label lines
		 plot.setBaseSectionOutlinePaint(Color.WHITE);  // Set the outline paint for sections to white
		 plot.setSectionOutlinesVisible(true);  // Ensure section outlines are visible
		 plot.setLabelLinkMargin(0.0);
		 return pieChart;
	}
    
    private JFreeChart plotPropertyChart(Set<String> elements, Map<String, Long> entries, Color[] colors, int fontSize) {
		DefaultPieDataset defaultCategoryDataset = new DefaultPieDataset();
		for (String entry : elements) {
			
			
			if(entry.equals("FULL")) {
				
				defaultCategoryDataset.setValue("LOADED", entries.get(entry));
			}
			else {
				defaultCategoryDataset.setValue(entry, entries.get(entry));
			}
			
		}
    
		JFreeChart pieChart = ChartFactory.createPieChart("", defaultCategoryDataset, false, false, false);

		PiePlot plot = (PiePlot) pieChart.getPlot();
		plot.setBackgroundPaint(Color.WHITE);
		plot.setOutlinePaint(new Color(73,73,73));
		plot.setOutlineStroke(new BasicStroke(0.3f));
		
		plot.setShadowPaint(Color.WHITE);
		int count = 0;
		for (String area : elements) {
			
			plot.setSectionPaint(area, colors[count]);
			plot.setLabelFont(new java.awt.Font("SansSerif", java.awt.Font.PLAIN, fontSize));
//        	 plot.setLabelGenerator(new CustomPieSectionLabelGenerator("{0} ({2})"));
			plot.setLabelGenerator(new CustomPieSectionLabelGenerator());
			plot.setLabelBackgroundPaint(Color.WHITE);
			plot.setLabelOutlinePaint(Color.WHITE);
			plot.setLabelOutlineStroke(null);
			plot.setShadowPaint(Color.WHITE);
			plot.setLabelBackgroundPaint(Color.WHITE);
//			plot.setShadowPaint(Color.WHITE);
			plot.setShadowPaint(null); 
			plot.setLabelShadowPaint(null);
//			plot.setSimpleLabels(true);
//			plot.setLabelLinkPaint(Color.BLACK);
//			plot.setLabelLinkStroke(new BasicStroke(1.0f));
			
			
			count++;
		}
		plot.setSectionPaint("LOADED", colors[count]);
		plot.setLabelPaint(Color.GRAY);
		plot.setLabelLinkStyle(PieLabelLinkStyle.STANDARD); // Ensure lines are straight
		plot.setLabelLinkPaint(Color.GRAY); // Set the color for the label lines
		plot.setLabelLinkStroke(new BasicStroke(1.0f)); // Set the thickness of the label lines
		plot.setBaseSectionOutlinePaint(Color.WHITE);  // Set the outline paint for sections to white
		plot.setSectionOutlinesVisible(true);  // Ensure section outlines are visible
		plot.setLabelLinkMargin(0.0);
		return pieChart;
	}
    
    
    private void plotChartInPdfAtGivenAxis(PdfContentByte pdfContentByte, JFreeChart propertyChart, float templateXAxis, float templateYAxis) {
		int width = 250;
		int height = 200;
		PdfTemplate pdfTemplate = pdfContentByte.createTemplate(width, height);

		Graphics2D graphics2d = pdfTemplate.createGraphics(width, height, new DefaultFontMapper());

		Rectangle2D rectangle2d = new Rectangle2D.Double(0, 0, width, height);
		
		propertyChart.draw(graphics2d, rectangle2d);

		graphics2d.dispose();
		pdfContentByte.addTemplate(pdfTemplate, templateXAxis, templateYAxis);

	}
    
    private static void addBorderColorInCell(Row borderRow, int i, CellStyle cellStyle) {
		Cell cell1 = borderRow.createCell(i);
		cell1.setCellStyle(cellStyle);
	}
    
    private static void addHeadingInCell(Row headerRow, int i, String heading, CellStyle cellStyle) {
		Cell cell1 = headerRow.createCell(i);
		cell1.setCellValue(heading);
		cell1.setCellStyle(cellStyle);
	}
		
}
