package com.ma.spoton.api.services;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.ConstraintViolationException;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

//import com.ma.spoton.api.dtos.CarrierDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.SuppliersDto;
//import com.ma.spoton.api.entities.Carriers;
import com.ma.spoton.api.entities.QFleet;
import com.ma.spoton.api.entities.QGuardEntryExit;
import com.ma.spoton.api.entities.QJob;
import com.ma.spoton.api.entities.QSuppliers;
import com.ma.spoton.api.entities.Suppliers;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.SuppliersMapper;
import com.ma.spoton.api.repositories.GuardEntryExitRepository;
import com.ma.spoton.api.repositories.SuppliersRepository;
//import com.ma.spoton.api.requests.CarriersRequest;
import com.ma.spoton.api.requests.SuppliersRequest;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.BooleanBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SuppliersServiceImpl implements SuppliersService{

	@Autowired
	private SuppliersMapper suppliersMapper;
	
	@Autowired
	private SuppliersRepository suppliersRepository;
	
	@Autowired
	private GuardEntryExitRepository guardEntryExitRepository;
	
	@Override
	@Transactional
	public SuppliersDto createSupplier(SuppliersRequest suppliersRequest) {
	    Suppliers supplier = suppliersMapper.mapToEntity(suppliersRequest);
	    Suppliers savedSupplier = null;
	    try {
	    	savedSupplier = suppliersRepository.save(supplier);
	    } catch (ConstraintViolationException | DataIntegrityViolationException e) {
	      log.error("Error occurred while creating supplier! Reason : {}", e.getMessage());
	      throw new ServiceException(ErrorCode.DUPLICATE_SUPPLIER);
	    }
	    return suppliersMapper.mapToDto(savedSupplier);
	}
	
	@Override
	@Transactional(readOnly = true)
	public PagedResponse<SuppliersDto> getSuppliers(Predicate predicate, Pageable pageable) {

		Page<Suppliers> suppliersPage = suppliersRepository.findAll(predicate, pageable);


		return PagedResponse.<SuppliersDto>builder()
			.list(suppliersPage.stream().map(supplier -> suppliersMapper.mapToDto(supplier))
				.collect(Collectors.toList()))
			.page(suppliersPage.getNumber()).size(suppliersPage.getSize())
			.totalElements(suppliersPage.getTotalElements()).build();
	}
	
	@Override
	@Transactional(readOnly = true)
	public List<SuppliersDto> getAllSuppliersList(Predicate predicate, String suppliers) {

		if (suppliers != null && !suppliers.isBlank()) {
			List<String> supplierNamesList = Arrays.asList(suppliers.split(","));
			BooleanBuilder supplierFilter = new BooleanBuilder();
			
			for (String supplierName : supplierNamesList) {
				supplierFilter.or(QSuppliers.suppliers.supplier.containsIgnoreCase(supplierName.trim()));
			}
			
			predicate = (predicate == null) ? supplierFilter : new BooleanBuilder(predicate).and(supplierFilter);
		}
		
		List<Suppliers> suppliersList = (List<Suppliers>) suppliersRepository.findAll(predicate);
		List<SuppliersDto> suppliersDtoList = suppliersList.stream().map(supplier -> suppliersMapper.mapToDto(supplier))
		.collect(Collectors.toList());
		
		return suppliersDtoList;
	}
	
	@Override
	@Transactional
	public void deleteSupplier(String supplierId) {
		Suppliers suppliers = suppliersRepository.findByUuid(supplierId)
				.orElseThrow(() -> new ServiceException(ErrorCode.SUPPLIER_NOT_FOUND, supplierId));
//		long fleetCount = fleetRepository.count(QFleet.fleet.carriers.eq(carriers));
//		if (fleetCount > 0) {
//			carriers.setIsActive(false);
//		} else {
//			carriersRepository.delete(carriers);
//		}
		long entryCount = guardEntryExitRepository.count(QGuardEntryExit.guardEntryExit.supplier.eq(suppliers));
		
		if(entryCount > 0) {
			suppliers.setIsActive(Boolean.FALSE);
			suppliersRepository.save(suppliers);
		}else {
			suppliersRepository.delete(suppliers);
		}
		
	}
	
	@Override
	@Transactional
	public void updateSupplier(String supplierId, SuppliersRequest supplierRequest) {
		Suppliers suppliers = suppliersRepository.findByUuid(supplierId)
				.orElseThrow(() -> new ServiceException(ErrorCode.SUPPLIER_NOT_FOUND, supplierId));
		suppliers.setSupplier(supplierRequest.getSupplier());
		suppliersRepository.save(suppliers);
	}
	
}
