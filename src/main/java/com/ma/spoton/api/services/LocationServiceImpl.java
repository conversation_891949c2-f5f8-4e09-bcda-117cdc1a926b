package com.ma.spoton.api.services;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.validation.ConstraintViolationException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.LocationDto;
import com.ma.spoton.api.dtos.LocationExportDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.UserAuthDto;
//import com.ma.spoton.api.entities.Carriers;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.GuardEntryExit;
import com.ma.spoton.api.entities.GuardEntryExit.Type;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.Job.FleetStatus;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.QJob;
import com.ma.spoton.api.entities.QLocation;
import com.ma.spoton.api.entities.QSpot;
import com.ma.spoton.api.entities.Role;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.entities.Spot.Status;
import com.ma.spoton.api.entities.Suppliers;
//import com.ma.spoton.api.entities.Spot;
//import com.ma.spoton.api.entities.Suppliers;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.LocationMapper;
//import com.ma.spoton.api.repositories.CarriersRepository;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.FleetRepository;
import com.ma.spoton.api.repositories.GuardEntryExitRepository;
import com.ma.spoton.api.repositories.JobRepository;
import com.ma.spoton.api.repositories.LocationRepository;
import com.ma.spoton.api.repositories.SpotRepository;
import com.ma.spoton.api.repositories.SuppliersRepository;
//import com.ma.spoton.api.repositories.SuppliersRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.requests.FleetEntryExitRequest;
import com.ma.spoton.api.requests.LocationRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.utils.CSVUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;

@Slf4j
@Service
public class LocationServiceImpl implements LocationService {

	@Autowired
	private LocationRepository locationRepository;

	@Autowired
	private ClientRepository clientRepository;

	@Autowired
	private SpotRepository spotRepository;

	@Autowired
	private FleetRepository fleetRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private GuardEntryExitRepository guardEntryExitRepository;

	@Autowired
	private JobRepository jobRepository;

//  @Autowired
//  private CarriersRepository carrierRepository;

	@Autowired
	private SuppliersRepository supplierRepository;

	@Autowired
	private LocationMapper locationMapper;

	@Autowired
	private FileHelper fileHelper;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private FleetService fleetService;

	@Autowired
	private FileService fileService;

	@Value("${application.location.map-image.path}")
	private String mapImagePath;

	@Override
	@Transactional
	public void createLocation(String clientId, LocationRequest locationRequest) {
//    log.info(">> createLocation({}, {})", clientId, locationRequest);
		Client client = clientRepository.findActiveByUuid(clientId)
				.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
		Location location = locationMapper.mapToEntity(locationRequest);
		location.setClient(client);
		try {
			long count = locationRepository.count(QLocation.location.client.eq(client));
			if (count == 0) {
				location.setIsDefault(Boolean.TRUE);
			}
			location = locationRepository.save(location);
		} catch (ConstraintViolationException | DataIntegrityViolationException e) {
			log.error("Error occurred while creating location! Reason : {}", e.getMessage());
			throw new ServiceException(ErrorCode.DUPLICATE_LOCATION_NAME);
		}

		if (StringUtils.isNotBlank(locationRequest.getMapImageBase64())) {
			String fileName = UUID.randomUUID().toString();
			try {
				fileHelper.uploadFile(fileName, mapImagePath, locationRequest.getMapImageBase64(), null);
				location.setMapImagePath(fileName);
				locationRepository.save(location);
			} catch (Exception e) {
				log.error("Some error occurred while uploading Location map image file to server.", e);
				throw new ServiceException(ErrorCode.LOCATION_MAP_IMAGE_UPLOAD, e.getMessage());
			}
		}
	}

	@Override
	@Transactional
	public void updateLocation(String clientId, String locationId, LocationRequest locationRequest) {
		log.info(">> updateLocation({}, {}, {})", clientId, locationId, locationRequest);
		Client client = clientRepository.findActiveByUuid(clientId)
				.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
		Location location = locationRepository.findByClientAndUuid(client, locationId)
				.orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND, locationId));
		location = locationMapper.updateEntity(locationRequest, location);
		if (StringUtils.isBlank(locationRequest.getLocationMapJson())) {
			location.setLocationMapJson(null);
		}
		if (StringUtils.isNotBlank(locationRequest.getMapImageBase64())) {
			String fileName = UUID.randomUUID().toString();
			log.info(">> fileName ({})", fileName);
			try {
				fileHelper.uploadFile(fileName, mapImagePath, locationRequest.getMapImageBase64(),
						location.getMapImagePath());
				location.setMapImagePath(fileName);
				locationRepository.save(location);
			} catch (Exception e) {
				log.error("Some error occurred while uploading Location map image file to server.", e);
				throw new ServiceException(ErrorCode.LOCATION_MAP_IMAGE_UPLOAD, e.getMessage());
			}
		}
		locationRepository.save(location);
	}

	@Override
	@Transactional
	public void deleteLocation(String clientId, String locationId) {
//    log.info(">> deleteLocation({}, {})", clientId, locationId);
		Client client = clientRepository.findActiveByUuid(clientId)
				.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
		Location location = locationRepository.findByClientAndUuid(client, locationId)
				.orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND, locationId));

		if (Boolean.TRUE.equals(location.getIsDefault())) {
			throw new ServiceException(ErrorCode.DELETE_LOCATION_IS_DEFAULT);
		}

		BooleanExpression spotBooleanExpression = QSpot.spot.location.eq(location);
		long spotCount = spotRepository.count(spotBooleanExpression);

		BooleanExpression jobBooleanExpression = QJob.job.pickupLocation.eq(location)
				.or(QJob.job.dropLocation.eq(location));
		long jobCount = jobRepository.count(jobBooleanExpression);

		BooleanExpression activeJobsBooleanExpression = QJob.job.status.eq(Job.Status.OPEN)
				.or(QJob.job.status.eq(Job.Status.IN_TRANSIT));
		BooleanExpression activeDropOrPickupBooleanExpression = jobBooleanExpression.and(activeJobsBooleanExpression);
		long activeJobsCounts = jobRepository.count(activeDropOrPickupBooleanExpression);

		if (activeJobsCounts > 0) {
			throw new ServiceException(ErrorCode.DELETE_LOCATION_WITH_ACTIVE_JOB);
		} else if (spotCount > 0) {
			location.setIsActive(false);
		} else {
			locationRepository.delete(location);
		}

//    if (spotCount > 0) {
//      location.setIsActive(false);
//    } else {
//      locationRepository.delete(location);
//    }
	}

	@Override
	@Transactional(readOnly = true)
	public LocationDto getLocation(String clientId, String locationId, String timeZone) {
//    log.info(">> getLocation({}, {}, {})", clientId, locationId, timeZone);
		Client client = clientRepository.findActiveByUuid(clientId)
				.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
		Location location = locationRepository.findByClientAndUuid(client, locationId)
				.orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND, locationId));
		return locationMapper.mapToDto(location, timeZone);
	}

	@Override
	@Transactional(readOnly = true)
	public PagedResponse<LocationDto> getLocations(String clientId, Predicate predicate, Pageable pageable,
			String timeZone, String assignedUserId) {
//    log.info(">> getLocations({}, {}, {}, {})", clientId, predicate, pageable, timeZone);
		
		UserAuthDto userAuthDto = AuthDetailsProvider.getLoggedInUser();
		User user = userRepository.findActiveByUuid(userAuthDto.getUserId())
				              .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userAuthDto.getUserId()));
		List<String> roles = user.getRoles().stream().map(Role::getRoleName).collect(Collectors.toList());
		if(roles.contains("SPOTTER")) {
	
			BooleanExpression spotterPredicate = QLocation.location.users.contains(user);
			predicate = spotterPredicate.and(predicate);
	    }
			
		if(clientId != null) {
			
			Client client = clientRepository.findActiveByUuid(clientId)
					.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));

			BooleanExpression mandatoryPredicate = QLocation.location.client.eq(client);
			
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		}
		
		
		if(assignedUserId != null) {
		
			User assignedToUser = userRepository.findOneActiveByUuid(assignedUserId);
			
		     if(assignedToUser.getRoles().stream().map(Role::getRoleName).collect(Collectors.toList()).contains("SPOTTER")) {
		    	 
		    	 BooleanExpression mandatoryPredicate2 = QLocation.location.users.contains(assignedToUser);
					
					if (Objects.isNull(predicate)) {
						predicate = mandatoryPredicate2;
					} else {
						predicate = mandatoryPredicate2.and(predicate);
					}
		     }	
		}
		
		

//		if (Objects.isNull(predicate)) {
//			predicate = mandatoryPredicate;
//		} else {
//			predicate = mandatoryPredicate.and(predicate);
//		}

		Page<Location> locationsPage = locationRepository.findAll(predicate, pageable);
		return PagedResponse.<LocationDto>builder()
				.list(locationsPage.stream().map(location -> locationMapper.mapToDto(location, timeZone))
						.collect(Collectors.toList()))
				.page(locationsPage.getNumber()).size(locationsPage.getSize())
				.totalElements(locationsPage.getTotalElements()).build();
	}

	@Override
	@Transactional
	public void createFleetEntryExit(String clientId, String locationId, FleetEntryExitRequest fleetEntryExitRequest,
			String userId, boolean sendNotification) {
//    log.info(">> createEntryExit({}, {}, {}, {}, {})", clientId, locationId, fleetEntryExitRequest,
//        userId, sendNotification);
		Client client = clientRepository.findActiveByUuid(clientId)
				.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
		
		Location location = locationRepository.findByClientAndUuid(client, locationId)
				.orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND, locationId));

//    Carriers carrier = carrierRepository.findByUuid(fleetEntryExitRequest.getCarrier())
//            .orElseThrow(() -> new ServiceException(ErrorCode.CARRIER_NOT_FOUND, fleetEntryExitRequest.getCarrier()));

		Fleet fleet = fleetRepository.findOneByUuid(fleetEntryExitRequest.getFleetId())
				.orElseThrow(() -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, fleetEntryExitRequest.getFleetId()));

		if (!fleet.getClients().contains(client)) {
			throw new ServiceException(ErrorCode.FLEET_NOT_FOUND, fleetEntryExitRequest.getFleetId());
		}

		if (Type.ENTRY.equals(fleetEntryExitRequest.getType())) {
			fleet.setIsActive(true);
		}
		if (Type.EXIT.equals(fleetEntryExitRequest.getType())) {
			fleetService.deactivateFleet(fleetEntryExitRequest.getFleetId());
		}
		
		GuardEntryExit guardEntryExit = new GuardEntryExit();
		guardEntryExit.setLocation(location);
		guardEntryExit.setFleet(fleet);
		guardEntryExit.setType(fleetEntryExitRequest.getType());
		guardEntryExit.setNotes(fleetEntryExitRequest.getNotes());
			
		List<Spot> previousSpots = spotRepository.findAllByFleet(fleet);
	      previousSpots.forEach(spot -> {
	    	  
	    	  spot.setFleet(null);
	     	  spot.setStatus(com.ma.spoton.api.entities.Spot.Status.EMPTY);
	     	  spot.setLastEmptiedTime(ZonedDateTime.now());
	     	  spot.setIsOccupied(false);
	    	  
	      }
	      );
		
		if (fleetEntryExitRequest.getSpotId() != null) {
					
			Spot spot = spotRepository.findByClientAndUuid(client, fleetEntryExitRequest.getSpotId()).orElseThrow(
					() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, fleetEntryExitRequest.getSpotId()));
			
			if (Type.ENTRY.equals(fleetEntryExitRequest.getType())) {
			
			spot.setFleet(fleet);
			spot.setStatus(Status.OCCUPIED);
			spot.setLastOccupiedTime(ZonedDateTime.now());
			fleet.setSpot(spot);
			}
			guardEntryExit.setSpot(spot);
		}
		if(fleetEntryExitRequest.getLoadStatus().equals("Empty")) {
			fleet.setFleetStatus(FleetStatus.EMPTY);
		}else if(fleetEntryExitRequest.getLoadStatus().equals("Loaded")){
			fleet.setFleetStatus(FleetStatus.FULL);
		}
		fleetRepository.save(fleet);
		guardEntryExit.setProNumber(fleetEntryExitRequest.getProNumber());
		guardEntryExit.setCarrier(fleetEntryExitRequest.getCarrier());
		if (fleetEntryExitRequest.getSupplier() != null) {
			Suppliers supplier = supplierRepository.findByUuid(fleetEntryExitRequest.getSupplier()).orElseThrow(
					() -> new ServiceException(ErrorCode.SUPPLIER_NOT_FOUND, fleetEntryExitRequest.getSupplier()));
			guardEntryExit.setSupplier(supplier);
		}

		guardEntryExit.setSub(fleetEntryExitRequest.getSub());
		guardEntryExit.setLoadStatus(fleetEntryExitRequest.getLoadStatus());
		guardEntryExit.setSequenceNumber(fleetEntryExitRequest.getSequenceNumber());
		guardEntryExit.setTractorNumber(fleetEntryExitRequest.getTractorNumber());
		guardEntryExit.setBillOfLandingType(fleetEntryExitRequest.getBillOfLandingType());
		guardEntryExit.setDateOfArrival(fleetEntryExitRequest.getDateOfArrival());
		guardEntryExit.setDateOfPickup(fleetEntryExitRequest.getDateOfPickup());
		guardEntryExit.setDueAtPlant(fleetEntryExitRequest.getDueOfPlant());
		if (fleetEntryExitRequest.getDriver() != null) {
//    	User driver = userRepository.findByUuid(fleetEntryExitRequest.getDriver())
//                .orElseThrow(() -> new ServiceException(ErrorCode.USER_NOT_FOUND, fleetEntryExitRequest.getDriver()));
			guardEntryExit.setDriver(fleetEntryExitRequest.getDriver());
		}

//    guardEntryExitRepository.save(guardEntryExit);
		GuardEntryExit savedEntry = guardEntryExitRepository.save(guardEntryExit);
		if (fleetEntryExitRequest.getBillOfLandingImage() != null) {
			fileService.entryExitBillOfLandingImage("EntryExit", savedEntry.getId(),
					fleetEntryExitRequest.getBillOfLandingImage());
		}

		Optional<User> user = userRepository.findActiveByUuid(userId);
		if (Type.ENTRY.equals(fleetEntryExitRequest.getType()) && Boolean.TRUE.equals(fleet.getIsHotTrailer())) {
			if (user.isPresent()) {
				var clientSuperVisorsAndClientUsers = userRepository.findAllByClientAndRoleNames(client.getId(),
						Set.of("CLIENT", "SUPERVISOR"));
				clientSuperVisorsAndClientUsers.forEach(toUser -> notificationService.createHotTrailerAlert(user.get(),
						toUser, fleet.getUnitNumber(), client.getClientName()));
			} else {
				log.warn("LoggedIn user is not found in database while sending Hot Trailer Alert!");
			}
		}

		if (sendNotification && user.isPresent()) {
			notificationService.createFleetEntryExitNotification(guardEntryExit, user.get());
		}
	}

	@Override
	@Transactional(readOnly = true)
	public Resource exportLocationsAsCSV(String clientId, Predicate predicate, String timeZone) {
//    log.info(">> exportLocationsAsCSV({}, {}, {})", clientId, predicate, timeZone);

		Client client = clientRepository.findActiveByUuid(clientId)
				.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));

		BooleanExpression mandatoryPredicate = QLocation.location.client.eq(client);

		if (Objects.isNull(predicate)) {
			predicate = mandatoryPredicate;
		} else {
			predicate = mandatoryPredicate.and(predicate);
		}

		var locations = locationRepository.findAll(mandatoryPredicate);

		List<LocationExportDto> locationExportDtos = new ArrayList<>();
		locations.forEach(location -> locationExportDtos.add(locationMapper.mapToExportDto(location, timeZone)));

		try {
			Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
			File tmpFolder = tmpDirPath.toFile();
			String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".csv";
			try (OutputStream outputStream = new FileOutputStream(fileName);) {
				String csvData = CSVUtils.toCSV(locationExportDtos, ',', true);
				outputStream.write(csvData.getBytes());
				outputStream.flush();
				return new InputStreamResource(new FileInputStream(fileName));
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting CSV!", e);
			throw new ServiceException(ErrorCode.LOCATION_EXPORT, e.getMessage());
		}
	}

	@Override
	@Transactional(readOnly = true)
	public Resource exportLocationsAsEXCEL(String clientId, Predicate predicate, String timeZone) {
		Client client = clientRepository.findActiveByUuid(clientId)
				.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));

		BooleanExpression mandatoryPredicate = QLocation.location.client.eq(client);

		if (Objects.isNull(predicate)) {
			predicate = mandatoryPredicate;
		} else {
			predicate = mandatoryPredicate.and(predicate);
		}

		var locations = locationRepository.findAll(mandatoryPredicate);

		List<LocationExportDto> locationExportDtos = new ArrayList<>();
		locations.forEach(location -> locationExportDtos.add(locationMapper.mapToExportDto(location, timeZone)));
		try {

//            	 Workbook workbook = new XSSFWorkbook();
//     	        Sheet sheet = workbook.createSheet("Locations");
//     	        Row headerRow = sheet.createRow(0);
//     	        headerRow.createCell(0).setCellValue("locationId");
//     	        headerRow.createCell(1).setCellValue("locationName");
//     	        headerRow.createCell(2).setCellValue("clientName");
//     	        headerRow.createCell(3).setCellValue("street");
//     	        headerRow.createCell(4).setCellValue("city");
//     	        headerRow.createCell(5).setCellValue("state");
//     	        headerRow.createCell(6).setCellValue("zip");
//     	        headerRow.createCell(7).setCellValue("country");
//     	        headerRow.createCell(8).setCellValue("remarks");
//     	        headerRow.createCell(9).setCellValue("isActive");
//     	        headerRow.createCell(10).setCellValue("isDefault");
//     	        headerRow.createCell(11).setCellValue("mapImageUrl");
//     	        headerRow.createCell(12).setCellValue("createdDate");
//     	        headerRow.createCell(13).setCellValue("lastModifiedDate");
//     	        headerRow.createCell(14).setCellValue("createdBy");
//     	        headerRow.createCell(15).setCellValue("lastModifiedBy"); 
			Workbook workbook = new XSSFWorkbook();
			Sheet sheet = workbook.createSheet("Locations");
			Row headerRow = sheet.createRow(0);

			Font boldFont = workbook.createFont();
			boldFont.setBold(true);
			boldFont.setColor(IndexedColors.WHITE.getIndex());

			CellStyle cellStyle = workbook.createCellStyle();
			cellStyle.setFont(boldFont);
			cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
			cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

			// Create cells with bold values
			Cell cell1 = headerRow.createCell(0);
			cell1.setCellValue("Location Id");
			cell1.setCellStyle(cellStyle);

			Cell cell2 = headerRow.createCell(1);
			cell2.setCellValue("Location Name");
			cell2.setCellStyle(cellStyle);

			Cell cell3 = headerRow.createCell(2);
			cell3.setCellValue("Client Name");
			cell3.setCellStyle(cellStyle);

			Cell cell4 = headerRow.createCell(3);
			cell4.setCellValue("Street");
			cell4.setCellStyle(cellStyle);

			Cell cell5 = headerRow.createCell(4);
			cell5.setCellValue("City");
			cell5.setCellStyle(cellStyle);

			Cell cell6 = headerRow.createCell(5);
			cell6.setCellValue("State");
			cell6.setCellStyle(cellStyle);

			Cell cell7 = headerRow.createCell(6);
			cell7.setCellValue("Zip");
			cell7.setCellStyle(cellStyle);

			Cell cell8 = headerRow.createCell(7);
			cell8.setCellValue("Country");
			cell8.setCellStyle(cellStyle);

			Cell cell9 = headerRow.createCell(8);
			cell9.setCellValue("Remarks");
			cell9.setCellStyle(cellStyle);

			Cell cell10 = headerRow.createCell(9);
			cell10.setCellValue("Is Active");
			cell10.setCellStyle(cellStyle);

			Cell cell11 = headerRow.createCell(10);
			cell11.setCellValue("Is Default");
			cell11.setCellStyle(cellStyle);

			Cell cell12 = headerRow.createCell(11);
			cell12.setCellValue("Map Image Url");
			cell12.setCellStyle(cellStyle);

			Cell cell13 = headerRow.createCell(12);
			cell13.setCellValue("Created Date");
			cell13.setCellStyle(cellStyle);

			Cell cell14 = headerRow.createCell(13);
			cell14.setCellValue("Last Modified Date");
			cell14.setCellStyle(cellStyle);

			Cell cell15 = headerRow.createCell(14);
			cell15.setCellValue("Created By");
			cell15.setCellStyle(cellStyle);

			Cell cell16 = headerRow.createCell(15);
			cell16.setCellValue("Last Modified By");
			cell16.setCellStyle(cellStyle);
			int rowNum = 1;
			for (LocationExportDto locationExportDto : locationExportDtos) {

				Row row = sheet.createRow(rowNum++);
				row.createCell(0).setCellValue(locationExportDto.getLocationId());
				row.createCell(1).setCellValue(locationExportDto.getLocationName());
				row.createCell(2).setCellValue(locationExportDto.getClientName());
				row.createCell(3).setCellValue(locationExportDto.getStreet());
				row.createCell(4).setCellValue(locationExportDto.getCity());
				row.createCell(5).setCellValue(locationExportDto.getState());
				row.createCell(6).setCellValue(locationExportDto.getZip());
				row.createCell(7).setCellValue(locationExportDto.getCountry());
				row.createCell(8).setCellValue(locationExportDto.getRemarks());
				if (locationExportDto.getIsActive() == true)
					row.createCell(9).setCellValue("true");
				else
					row.createCell(9).setCellValue("false");
				if (locationExportDto.getIsDefault() != null)
					row.createCell(10).setCellValue(locationExportDto.getIsDefault());
				else
					row.createCell(10).setCellValue("---");
				row.createCell(11).setCellValue(locationExportDto.getMapImageUrl());
				row.createCell(12).setCellValue(locationExportDto.getCreatedDate());
				row.createCell(13).setCellValue(locationExportDto.getLastModifiedDate());
				row.createCell(14).setCellValue(locationExportDto.getCreatedBy());
				row.createCell(15).setCellValue(locationExportDto.getLastModifiedBy());

			}
			Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
			File tmpFolder = tmpDirPath.toFile();
			String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
			try (OutputStream outputStream = new FileOutputStream(fileName)) {
				workbook.write(outputStream);
				return new InputStreamResource(new FileInputStream(fileName));
			}

		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.FLEET_EXPORT, e.getMessage());
		}

	}

	@Override
	@Transactional
	public void makeLocationDefault(String clientId, String locationId) {
//    log.info(">> makeLocationDefault({}, {})", clientId, locationId);
		Client client = clientRepository.findActiveByUuid(clientId)
				.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
		Location location = locationRepository.findByClientAndUuid(client, locationId)
				.orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND, locationId));

		Optional<Location> defaultLocation = locationRepository.findDefaultByClient(client);
		if (defaultLocation.isPresent()) {
			defaultLocation.get().setIsDefault(Boolean.FALSE);
		}

		location.setIsDefault(Boolean.TRUE);
	}

	@Override
	@Transactional
	public void deleteGuardEntryExit(String guardEntryExitId) {
//    log.info(">> deleteGuardEntryExit({})", guardEntryExitId);
		GuardEntryExit guardEntryExit = guardEntryExitRepository.findByUuid(guardEntryExitId)
				.orElseThrow(() -> new ServiceException(ErrorCode.GUARD_ENTRY_EXIT_NOT_FOUND, guardEntryExitId));
		guardEntryExitRepository.delete(guardEntryExit);
	}

	@Override
	public void DeleteLocationImage(String clientId, String locationId) {

//	log.info(">> deleteLocationImage({}, {})", clientId, locationId);
		Client client = clientRepository.findActiveByUuid(clientId)
				.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
		Location location = locationRepository.findByClientAndUuid(client, locationId)
				.orElseThrow(() -> new ServiceException(ErrorCode.LOCATION_NOT_FOUND, locationId));
		location.setMapImagePath(null);
		locationRepository.save(location);

	}

}
