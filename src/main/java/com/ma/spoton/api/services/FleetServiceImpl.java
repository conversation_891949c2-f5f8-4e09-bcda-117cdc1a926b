package com.ma.spoton.api.services;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Image;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.ColumnText;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.controllers.UserController;
import com.ma.spoton.api.dtos.*;
import com.ma.spoton.api.entities.*;
import com.ma.spoton.api.entities.Job.FleetStatus;
import com.ma.spoton.api.entities.Spot.Status;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.AuditMapper;
import com.ma.spoton.api.mappers.FleetMapper;
import com.ma.spoton.api.mappers.GuardEntryExitMapper;
import com.ma.spoton.api.mappers.TrailerLogMapper;
import com.ma.spoton.api.mappers.UserMapper;
import com.ma.spoton.api.repositories.*;
import com.ma.spoton.api.requests.ClientFleetRequest;
import com.ma.spoton.api.requests.FleetRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.utils.CSVUtils;
import com.ma.spoton.api.utils.DateTimeUtils;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ConstraintViolationException;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.poi.util.IOUtils;

@Slf4j
@Service
public class FleetServiceImpl implements FleetService {

	@Autowired
	private MessageRepository messagerepository;

	@Autowired
	private FleetRepository fleetRepository;

	@Autowired
	private JobRepository jobRepository;

	@Autowired
	private ClientRepository clientRepository;

	@Autowired
	private SpotRepository spotRepository;

	@Autowired
	private FleetMapper fleetMapper;

	@Autowired
	private GuardEntryExitMapper guardEntryExitMapper;

	@Autowired
	private AuditMapper auditMapper;
	
	@Autowired
	private TrailerLogMapper trailerLogMapper;

	@Autowired
	private GuardEntryExitRepository guardEntryExitRepository;
	
	@Autowired
	private TrailerLogRepository trailerLogRepository;

	@Autowired
	private UserMapper usermapper;

	@Autowired
	private DuplicateTrailersRepository duplicateTrailersRepository;

	@Autowired
	private TrailerAuditRepository trailerAuditRepository;
	
//	@Autowired
//	private CarriersRepository carrierRepository;
	
	@Autowired
	private SuppliersRepository suppliersRepository;
	
	@Value("${application.cdn1}")
	private String cdn;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(UserController.class);
	
	@Transactional
	public FleetDto createFleet(FleetRequest fleetRequest, List<String> clientIds) {
//		log.info(">> createFleet({}, {})", fleetRequest, clientIds);
		long start = System.currentTimeMillis();
		UserAuthDto userauthdto = AuthDetailsProvider.getLoggedInUser();
		User user = usermapper.mapToUser(userauthdto);

		Fleet deactivatedFleet = fleetRepository.findInactiveByUnitNumber(fleetRequest.getUnitNumber());
//		Carriers carrier = null;
//		if (StringUtils.isNotBlank(fleetRequest.getCarrier())) {
//			carrier = carrierRepository.findByUuid(fleetRequest.getCarrier()).orElseThrow(
//				() -> new ServiceException(ErrorCode.CARRIER_NOT_FOUND, fleetRequest.getCarrier()));
//		}
		if (deactivatedFleet != null) {
			deactivatedFleet.setIsActive(Boolean.TRUE);
			deactivatedFleet.setCarrier(fleetRequest.getCarrier());
//			deactivatedFleet.setCarriers(carrier);
			deactivatedFleet.setType(fleetRequest.getType());
			deactivatedFleet.setFleetStatus(fleetRequest.getFleetStatus());
			deactivatedFleet.setOwner(fleetRequest.getOwner());
			deactivatedFleet.setRemarks(fleetRequest.getRemarks());
			deactivatedFleet.setIsHotTrailer(fleetRequest.getIsHotTrailer());
//    	deactivatedFleet.setCreatedBy(user);
//      deactivatedFleet.setLastModifiedBy(user);
			Spot spot = null;
			Fleet finalFleet = deactivatedFleet;
			if (StringUtils.isNotBlank(fleetRequest.getSpotId())) {
				spot = spotRepository.findActiveByUuid(fleetRequest.getSpotId()).orElseThrow(
					() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, fleetRequest.getSpotId()));
				finalFleet.setSpot(spot);
			}
			if (CollectionUtils.isNotEmpty(fleetRequest.getClientIds())) {
				List<String> clientIdsToAssign = fleetRequest.getClientIds();
				if (CollectionUtils.isNotEmpty(clientIds)) {
					clientIdsToAssign = fleetRequest.getClientIds().stream().filter(clientIds::contains)
						.collect(Collectors.toList());
				}
				Set<Client> clientsToBeAssigned = CollectionUtils.isNotEmpty(clientIdsToAssign)
					? clientRepository.findAllByUuidIn(clientIdsToAssign)
					: Set.of();

				clientsToBeAssigned.forEach(client -> client.getFleets().add(finalFleet));
			}
			if (CollectionUtils.isNotEmpty(fleetRequest.getCommodities())) {
				Set<Suppliers> commodities = suppliersRepository.findAllByUuidIn(fleetRequest.getCommodities());
				deactivatedFleet.setCommodity(commodities);
				// Add fleet to each supplier's fleets collection (owning side)
				commodities.forEach(supplier -> supplier.getFleets().add(finalFleet));
			}
			Fleet savedFleet = null;
			try {
				savedFleet = fleetRepository.save(finalFleet);
			} catch (ConstraintViolationException | DataIntegrityViolationException e) {
				log.error("Error occurred while creating fleet! Reason : {}", e.getMessage());
				throw new ServiceException(ErrorCode.DUPLICATE_FLEET_UNIT_NUMBER);
			}
			if (Objects.nonNull(spot)) {
				spot.setFleet(savedFleet);
				spot.setStatus(Status.OCCUPIED);
				spot.setLastOccupiedTime(ZonedDateTime.now());
			}

			return fleetMapper.mapToDto(savedFleet);

		} else {
			Fleet fleet = fleetMapper.mapToEntity(fleetRequest);
//			Carriers fleetCarrier = null;
//			if (StringUtils.isNotBlank(fleetRequest.getCarrier())) {
//				fleetCarrier = carrierRepository.findByUuid(fleetRequest.getCarrier()).orElseThrow(
//					() -> new ServiceException(ErrorCode.CARRIER_NOT_FOUND, fleetRequest.getCarrier()));
//				fleet.setCarriers(fleetCarrier);
//			}
			
			if (CollectionUtils.isNotEmpty(fleetRequest.getCommodities())) {
				LoggerUtil.logSLA(LOGGER, "************fleet*****", start, "fleet**");
				Set<Suppliers> commodities = suppliersRepository.findAllByUuidIn(fleetRequest.getCommodities());
				fleet.setCommodity(commodities);
				// Add fleet to each supplier's fleets collection (owning side)
				commodities.forEach(supplier -> supplier.getFleets().add(fleet));
			}
			
			List<DuplicateTrailers> duplicateTrailers = duplicateTrailersRepository.findAll();
			for (DuplicateTrailers duplicateTrailer : duplicateTrailers) {
				if (duplicateTrailer.getUnitNumber().toLowerCase().equals(fleet.getUnitNumber().toLowerCase()))
					throw new ServiceException(ErrorCode.DUPLICATE_TRAILER);
			}


			Spot spot = null;
			if (StringUtils.isNotBlank(fleetRequest.getSpotId())) {
				spot = spotRepository.findActiveByUuid(fleetRequest.getSpotId()).orElseThrow(
					() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, fleetRequest.getSpotId()));
				fleet.setSpot(spot);
			}
			if (CollectionUtils.isNotEmpty(fleetRequest.getClientIds())) {
				List<String> clientIdsToAssign = fleetRequest.getClientIds();
				if (CollectionUtils.isNotEmpty(clientIds)) {
					clientIdsToAssign = fleetRequest.getClientIds().stream().filter(clientIds::contains)
						.collect(Collectors.toList());
				}
				Set<Client> clientsToBeAssigned = CollectionUtils.isNotEmpty(clientIdsToAssign)
					? clientRepository.findAllByUuidIn(clientIdsToAssign)
					: Set.of();

				clientsToBeAssigned.forEach(client -> client.getFleets().add(fleet));
			}
			Fleet savedFleet = null;
			try {
				savedFleet = fleetRepository.save(fleet);
			} catch (ConstraintViolationException | DataIntegrityViolationException e) {
				log.error("Error occurred while creating fleet! Reason : {}", e.getMessage());
				throw new ServiceException(ErrorCode.DUPLICATE_FLEET_UNIT_NUMBER);
			}
			if (Objects.nonNull(spot)) {
				spot.setFleet(savedFleet);
				spot.setStatus(Status.OCCUPIED);
				spot.setLastOccupiedTime(ZonedDateTime.now());
			}

			return fleetMapper.mapToDto(savedFleet);
		}


	}

	@Override
	@Transactional
	public void updateFleet(String fleetId, FleetRequest fleetRequest) {
//		log.info(">> updateFleet({}, {})", fleetId, fleetRequest);
		
		Fleet fleet = fleetRepository.findByUuid(fleetId)
			.orElseThrow(() -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, fleetId));
		Spot previousSpot = fleet.getSpot();
		if (StringUtils.isNotBlank(fleetRequest.getSpotId())) {
			if (fleet.getSpot() == null) {
				
				Spot updatedSpot = spotRepository.findActiveByUuid(fleetRequest.getSpotId()).orElseThrow(
						() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, fleetRequest.getSpotId()));
				TrailerLog log = new TrailerLog();
			    log.setFleet(fleet);
			    log.setActions("Edited trailer "+ fleet.getUnitNumber() + " with the location "+ updatedSpot.getLocation().getLocationName() + " - " + updatedSpot.getSpotName());
			    log.setIsActive(true);
			    trailerLogRepository.save(log);
			    
			}else if (!fleetRequest.getSpotId().equals(fleet.getSpot().getUuid())) {
				
				Spot updatedSpot = spotRepository.findActiveByUuid(fleetRequest.getSpotId()).orElseThrow(
						() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, fleetRequest.getSpotId()));
				TrailerLog log = new TrailerLog();
			    log.setFleet(fleet);
			    log.setActions("Edited trailer "+ fleet.getUnitNumber() + " with the location "+ updatedSpot.getLocation().getLocationName() + " - " + updatedSpot.getSpotName());
			    log.setIsActive(true);
			    trailerLogRepository.save(log);
			} 
		}

		fleet = fleetMapper.updateEntity(fleetRequest, fleet);

		if (StringUtils.isNotBlank(fleetRequest.getSpotId())) {
			Spot spot = spotRepository.findActiveByUuid(fleetRequest.getSpotId()).orElseThrow(
				() -> new ServiceException(ErrorCode.SPOT_NOT_FOUND, fleetRequest.getSpotId()));
			fleet.setSpot(spot);
			spot.setFleet(fleet);
			spot.setStatus(Status.OCCUPIED);
			spot.setLastOccupiedTime(ZonedDateTime.now());
			if (Objects.nonNull(previousSpot) && !spot.equals(previousSpot)) {
				previousSpot.setFleet(null);
				previousSpot.setStatus(Status.EMPTY);
				previousSpot.setLastEmptiedTime(ZonedDateTime.now());
			}
		}
		if (fleetRequest.getClientIds().size() > 0) {

			Fleet finalFleet = fleet;
			Set<Client> previousClients = fleet.getClients();
			previousClients.forEach(client -> client.getFleets().remove(finalFleet));
			List<Client> clients = clientRepository.findAllClientsByClientId(fleetRequest.getClientIds());
			clients.forEach(client -> client.getFleets().add(finalFleet));

		}

		fleetRepository.save(fleet);
	}

	@Override
	@Transactional
	public void deleteFleet(String fleetId) {
//		log.info(">> deleteFleet({})", fleetId);
		Fleet fleet = fleetRepository.findByUuid(fleetId)
			.orElseThrow(() -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, fleetId));
		long jobCount = jobRepository.count(QJob.job.fleet.eq(fleet));
		long spotCount = spotRepository.count(QSpot.spot.fleet.eq(fleet));
		if (jobCount > 0 || spotCount > 0 || CollectionUtils.isNotEmpty(fleet.getClients())) {
			throw new ServiceException(ErrorCode.DELETE_FLEET_JOB_ASSOCIATION, fleetId);
		} else {
			fleetRepository.delete(fleet);
		}
	}

	@Override
	@Transactional
	public void deactivateFleet(String fleetId) {
//		log.info(">> deactivateFleet({})", fleetId);
		Fleet fleet = fleetRepository.findByUuid(fleetId)
			.orElseThrow(() -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, fleetId));
		List<Job> jobs = jobRepository.findAllByFleetAndStatusIn(fleet,
			List.of(com.ma.spoton.api.entities.Job.Status.OPEN,
				com.ma.spoton.api.entities.Job.Status.IN_TRANSIT));
		if (jobs.isEmpty()) {
			jobs.forEach(job -> job.setIsActive(Boolean.FALSE));
			List<Spot> spots = spotRepository.findAllByFleet(fleet);
			spots.forEach(spot ->
				{
					spot.setFleet(null);
					spot.setStatus(Status.EMPTY);
					spot.setLastEmptiedTime(ZonedDateTime.now());
				}
			);
			fleet.setIsActive(Boolean.FALSE);
			fleet.setSpot(null);
			fleetRepository.save(fleet);
		} else {
			throw new ServiceException(ErrorCode.DEACTIVATE_FLEET_JOB_ASSOCIATION, fleetId);
		}
	}

	@Override
	@Transactional
	public void activateFleet(String fleetId) {
//		log.info(">> activateFleet({})", fleetId);
		Fleet fleet = fleetRepository.findByUuid(fleetId)
			.orElseThrow(() -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, fleetId));
		fleet.setIsActive(Boolean.TRUE);
		fleetRepository.save(fleet);
	}

	@Override
	@Transactional(readOnly = true)
	public FleetDto getFleet(String fleetId, String timeZone, List<String> clientIds) {
//		log.info(">> getFleet({}, {}, {})", fleetId, timeZone, clientIds);
		Fleet fleet = fleetRepository.findByUuid(fleetId)
			.orElseThrow(() -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, fleetId));
		if (CollectionUtils.isNotEmpty(clientIds) && (CollectionUtils.isEmpty(fleet.getClients())
			|| fleet.getClients().stream().map(Client::getUuid).noneMatch(clientIds::contains))) {
			throw new ServiceException(ErrorCode.FLEET_NOT_FOUND, fleetId);
		}
		FleetDto fleetDto = fleetMapper.mapToDto(fleet, timeZone);
		fleetDto.setClientIds(CollectionUtils.isNotEmpty(fleet.getClients())
			? fleet.getClients().stream().map(Client::getUuid).collect(Collectors.toSet())
			: Set.of());
		return fleetDto;
	}

	@Override
	@Transactional(readOnly = true)
	public PagedResponse<FleetDto> getFleets(Predicate predicate, Pageable pageable, String timeZone,
											 List<String> clientIds,String locationIds, String unit_sequenceNumber) {
//		log.info(">> getFleets({}, {}, {}, {}, {})", predicate, pageable, timeZone, clientIds, locationIds);
		BooleanExpression mandatoryPredicate = QFleet.fleet.clients.any().uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		} else {
			mandatoryPredicate = null;
		}
		
		if (locationIds != null && !locationIds.isBlank()) {
			List<String> locationIdsList = Arrays.asList(locationIds.split(","));

		    BooleanExpression locationFilter = QFleet.fleet.spot.location.uuid.in(locationIdsList);
		    predicate = (predicate == null) ? locationFilter : ((BooleanExpression) predicate).and(locationFilter);
		}
		
		if(unit_sequenceNumber != null && !unit_sequenceNumber.isBlank()) {
			BooleanExpression unitNumberFilter = QFleet.fleet.unitNumber.containsIgnoreCase(unit_sequenceNumber);
			BooleanExpression sequenceNumberFilter = QFleet.fleet.sequenceNumber.containsIgnoreCase(unit_sequenceNumber);
			BooleanExpression searchFinalFilter = unitNumberFilter.or(sequenceNumberFilter);
			
			if (predicate == null) {
				predicate = searchFinalFilter;
			} else {
				predicate = ExpressionUtils.allOf(predicate, searchFinalFilter);
			}
		}

		Page<Fleet> fleetsPage = fleetRepository.findAll(predicate, pageable);

		BooleanExpression emptyFleetCriteria = QFleet.fleet.fleetStatus.eq(FleetStatus.EMPTY);
		Long emptyFleetCount = fleetRepository
			.count(Objects.nonNull(mandatoryPredicate) ? mandatoryPredicate.and(emptyFleetCriteria)
				: emptyFleetCriteria);

		BooleanExpression fullFleetCriteria = QFleet.fleet.fleetStatus.eq(FleetStatus.FULL);
		Long fullFleetCount = fleetRepository
			.count(Objects.nonNull(mandatoryPredicate) ? mandatoryPredicate.and(fullFleetCriteria)
				: fullFleetCriteria);

		return PagedResponse.<FleetDto>builder()
			.list(fleetsPage.stream().map(fleet -> fleetMapper.mapToDto(fleet, timeZone))
				.collect(Collectors.toList()))
			.page(fleetsPage.getNumber()).size(fleetsPage.getSize())
			.totalElements(fleetsPage.getTotalElements())
			.tally(Map.of("totalEmpty", emptyFleetCount, "totalFull", fullFleetCount)).build();
	}

	@Override
	@Transactional
	public void assignFleets(String clientId, ClientFleetRequest clientFleetRequest) {
//		log.info(">> assignFleets({}, {})", clientId, clientFleetRequest);
		Client client = clientRepository.findActiveByUuid(clientId)
			.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
		List<Fleet> fleets = fleetRepository.findAllByUuidIn(clientFleetRequest.getFleetIds());
		if (CollectionUtils.isEmpty(fleets)) {
			throw new ServiceException(ErrorCode.FLEET_IDS_ASSIGN_FLEET_REQUEST_INVALID);
		}
		client.getFleets().addAll(fleets);
		
		//Trailer activities logged
		List<TrailerLog> logs = new ArrayList<>();
		for(Fleet fleet: fleets) {
		    TrailerLog log = new TrailerLog();
		    log.setActions("Assigned trailer ("+ fleet.getUnitNumber() + ") to the client " + client.getClientName());
		    log.setFleet(fleet);
		    log.setIsActive(true);
		    logs.add(log);
		}
		trailerLogRepository.saveAll(logs);
	}


	@Override
	@Transactional
	public void assignFleetsAfterMerging(FleetMergeDto fleetMergeDto) {
		// TODO Auto-generated method stub
		Fleet fleet;
		Client client = clientRepository.findActiveByUuid(fleetMergeDto.getClient())
			.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, fleetMergeDto.getClient()));
		if (fleetMergeDto.getAssignedUnitNumber() != null) {
			fleet = fleetRepository.findByUnitNumber(fleetMergeDto.getAssignedUnitNumber());
		} else {
			fleet = fleetRepository.findByUnitNumber(fleetMergeDto.getSelectedUnitNumber());
		}
		client.getFleets().add(fleet);
	}


	@Override
	@Transactional
	public void unassignFleets(String clientId, ClientFleetRequest clientFleetRequest) {
//		log.info(">> unassignFleets({}, {})", clientId, clientFleetRequest);
		Client client = clientRepository.findActiveByUuid(clientId)
			.orElseThrow(() -> new ServiceException(ErrorCode.CLIENT_NOT_FOUND, clientId));
		List<Fleet> fleets = fleetRepository.findAllByUuidIn(clientFleetRequest.getFleetIds());
		if (CollectionUtils.isEmpty(fleets)) {
			throw new ServiceException(ErrorCode.FLEET_IDS_ASSIGN_FLEET_REQUEST_INVALID);
		}
		client.getFleets().removeAll(fleets);
	}

	@Override
	@Transactional(readOnly = true)
	public PagedResponse<GuardEntryExitDto> getFleetEntryExits(Predicate predicate, Pageable pageable,
															   String timeZone, List<String> clientIds, String fromDate, String toDate) {
//		log.info(">> getFleetEntryExits({}, {}, {}, {})", predicate, pageable, timeZone, clientIds);

		BooleanExpression mandatoryPredicate =
			QGuardEntryExit.guardEntryExit.location.client.uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		}
		
		if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
			ZonedDateTime fromDateTime = DateTimeUtils
					.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT).atTime(LocalTime.MIN)
					.atZone(ZoneId.of(timeZone));
			ZonedDateTime toDateTime = DateTimeUtils
					.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT).atTime(LocalTime.MAX)
					.atZone(ZoneId.of(timeZone));
			BooleanExpression betweenDateCriteria = QGuardEntryExit.guardEntryExit.createdDate.between(fromDateTime, toDateTime);
			predicate = Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
		}

		Page<GuardEntryExit> entryExitsPage = guardEntryExitRepository.findAll(predicate, pageable);
		entryExitsPage.forEach(bol -> {
			if(bol.getBillOfLandingImage() != null) {
				String currentImagePath = bol.getBillOfLandingImage();
		        String newImagePath = cdn + "/" + "EntryExit" + "/" + currentImagePath;
		        bol.setBillOfLandingImage(newImagePath);
			}
		});
		
		return PagedResponse.<GuardEntryExitDto>builder()
			.list(entryExitsPage.stream().map(entryExit -> {
				GuardEntryExitDto dto = guardEntryExitMapper.mapToDto(entryExit);
				dto.setAudit(auditMapper.mapToDto(entryExit, timeZone));
				return dto;
			}).collect(Collectors.toList())).page(entryExitsPage.getNumber())
			.size(entryExitsPage.getSize()).totalElements(entryExitsPage.getTotalElements()).build();
	}

	@Override
	@Transactional(readOnly = true)
	public Resource exportFleetsAsCSV(Predicate predicate, String timeZone, List<String> clientIds) {
//		log.info(">> exportFleetsAsCSV({}, {}, {})", predicate, timeZone, clientIds);
		BooleanExpression mandatoryPredicate = QFleet.fleet.clients.any().uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		}
		var fleets = fleetRepository.findAll(predicate);
		List<FleetExportDto> fleetExportDtos = new ArrayList<>();
		fleets.forEach(fleet -> fleetExportDtos.add(fleetMapper.mapToExportDto(fleet, timeZone)));
		for (FleetExportDto fleetExportDto : fleetExportDtos) {
			if (fleetExportDto.getSpot() != null) {
				fleetExportDto.setLocationName(fleetExportDto.getSpot().getLocation().getLocationName());
				fleetExportDto.setSpotName(fleetExportDto.getSpot().getSpotName());
				fleetExportDto.setSpot(null);
			}
		}
		List<FleetCsvExportDto> fleetCsvExportDtos = new ArrayList<>();
		fleetExportDtos.forEach(fleetExportDto -> fleetCsvExportDtos.add(fleetMapper.mapToFleetCsvExportDto(fleetExportDto)));

		try {
			Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
			File tmpFolder = tmpDirPath.toFile();
			String fileName =
				tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".csv";
			try (OutputStream outputStream = new FileOutputStream(fileName);) {
				String csvData = CSVUtils.toCSV(fleetCsvExportDtos, ',', true);
				outputStream.write(csvData.getBytes());
				outputStream.flush();
				return new InputStreamResource(new FileInputStream(fileName));
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting CSV!", e);
			throw new ServiceException(ErrorCode.FLEET_EXPORT, e.getMessage());
		}
	}

	@Override
	@Transactional(readOnly = true)
	public Resource exportFleetsAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds,String locationIds) {
		// TODO Auto-generated method stub
//		log.info(">> exportFleetsAsCSV({}, {}, {})", predicate, timeZone, clientIds);
		BooleanExpression mandatoryPredicate = QFleet.fleet.clients.any().uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		}
		
		if (locationIds != null && !locationIds.isBlank()) {
		    List<String> locationIdsList = Arrays.asList(locationIds.split(","));
		    BooleanExpression locationFilter = QFleet.fleet.spot.location.uuid.in(locationIdsList);

		    predicate = ExpressionUtils.allOf(predicate, locationFilter);
		}


		var fleets = fleetRepository.findAll(predicate);

		List<FleetExportDto> fleetExportDtos = new ArrayList<>();
		fleets.forEach(fleet -> fleetExportDtos.add(fleetMapper.mapToExportDto(fleet, timeZone)));
		try {


//	    	Workbook workbook = new XSSFWorkbook();
//	        Sheet sheet = workbook.createSheet("Fleets");
//	        Row headerRow = sheet.createRow(0);
//
//
//	        headerRow.createCell(0).setCellValue("carrier");
//	        headerRow.createCell(1).setCellValue("type");
//	        headerRow.createCell(2).setCellValue("unitNumber");
//	        headerRow.createCell(3).setCellValue("remarks");
//	        headerRow.createCell(4).setCellValue("isActive");
//	        headerRow.createCell(5).setCellValue("owner");
//	        headerRow.createCell(6).setCellValue("createdDate");
//	        headerRow.createCell(7).setCellValue("createdBy");

			Workbook workbook = new XSSFWorkbook();
			Sheet sheet = workbook.createSheet("Fleets");
			Row headerRow = sheet.createRow(0);

			Font boldFont = workbook.createFont();
			boldFont.setBold(true);
			boldFont.setColor(IndexedColors.WHITE.getIndex());
			CellStyle cellStyle = workbook.createCellStyle();
			cellStyle.setFont(boldFont);
			cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
			cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

			// Create cells with bold values
			Cell cell1 = headerRow.createCell(0);
			cell1.setCellValue("Type");
			cell1.setCellStyle(cellStyle);

			Cell cell2 = headerRow.createCell(1);
			cell2.setCellValue("Unit Number");
			cell2.setCellStyle(cellStyle);
			
			Cell cell3 = headerRow.createCell(2);
			cell3.setCellValue("Carrier");
			cell3.setCellStyle(cellStyle);
			
			Cell cell4 = headerRow.createCell(3);
			cell4.setCellValue("Trailer Status");
			cell4.setCellStyle(cellStyle);
			
			Cell cell5 = headerRow.createCell(4);
			cell5.setCellValue("Location");
			cell5.setCellStyle(cellStyle);
			
			Cell cell6 = headerRow.createCell(5);
			cell6.setCellValue("Sequence Number");
			cell6.setCellStyle(cellStyle);
			
			Cell cell7 = headerRow.createCell(6);
			cell7.setCellValue("Commodity");
			cell7.setCellStyle(cellStyle);

			Cell cell8 = headerRow.createCell(7);
			cell8.setCellValue("Notes");
			cell8.setCellStyle(cellStyle);
			
			Cell cell9 = headerRow.createCell(8);
			cell9.setCellValue("Created By");
			cell9.setCellStyle(cellStyle);
			
//			Cell cell9 = headerRow.createCell(8);
//			cell9.setCellValue("Last Updated");
//			cell9.setCellStyle(cellStyle);

//			Cell cell5 = headerRow.createCell(4);
//			cell5.setCellValue("Is Active");
//			cell5.setCellStyle(cellStyle);
//
//			Cell cell6 = headerRow.createCell(5);
//			cell6.setCellValue("Owner");
//			cell6.setCellStyle(cellStyle);
//
//			Cell cell7 = headerRow.createCell(6);
//			cell7.setCellValue("Created Date");
//			cell7.setCellStyle(cellStyle);
//
//			Cell cell10 = headerRow.createCell(9);
//			cell10.setCellValue("Spot");
//			cell10.setCellStyle(cellStyle);

			int rowNum = 1;
			for (FleetExportDto fleetExportDto : fleetExportDtos) {
				Row row = sheet.createRow(rowNum++);
				row.createCell(0).setCellValue(fleetExportDto.getType().toString());
				row.createCell(1).setCellValue(fleetExportDto.getUnitNumber());
				row.createCell(2).setCellValue(fleetExportDto.getCarrier());
				if (fleetExportDto.getFleetStatus() != null && fleetExportDto.getFleetStatus() == Job.FleetStatus.FULL) {
					row.createCell(3).setCellValue("LOADED");
				}else if(fleetExportDto.getFleetStatus() != null) {
					row.createCell(3).setCellValue(fleetExportDto.getFleetStatus().toString());
				}
				if (fleetExportDto.getSpot() != null) {
					//LoggerUtil.logSLA(LOGGER, "fleetExport EXCEL :" +fleetExportDto.getLocationName(), System.currentTimeMillis(), "exportMovesAsExcel completed");
					row.createCell(4).setCellValue(fleetExportDto.getSpot().getLocation().getLocationName()+" "+fleetExportDto.getSpot().getSpotName());
					//row.createCell(9).setCellValue(fleetExportDto.getSpot().getSpotName());
				}
				row.createCell(5).setCellValue(fleetExportDto.getSequenceNumber());
				
				String commodityNames = fleetExportDto.getCommodity().stream()
					    .map(SuppliersDto::getSupplier) 
					    .collect(Collectors.joining(", "));
					
				row.createCell(6).setCellValue(commodityNames);
				row.createCell(7).setCellValue(fleetExportDto.getRemarks());
//				if (fleetExportDto.getIsActive() == true)
//					row.createCell(4).setCellValue("True");
//				else
//					row.createCell(4).setCellValue("False");
//				row.createCell(5).setCellValue(fleetExportDto.getOwner());
//				row.createCell(6).setCellValue(fleetExportDto.getCreatedDate());
				row.createCell(8).setCellValue(fleetExportDto.getCreatedBy());
//				row.createCell(8).setCellValue(fleetExportDto.getLastModifiedDate());
				
			}
			Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
			File tmpFolder = tmpDirPath.toFile();
			String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
			try (OutputStream outputStream = new FileOutputStream(fileName)) {
				workbook.write(outputStream);
				return new InputStreamResource(new FileInputStream(fileName));
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.FLEET_EXPORT, e.getMessage());
		}	


	}
	
	@Override
	@Transactional(readOnly = true)
	public Resource exportFleetsAsPDF(Predicate predicate, String timeZone, List<String> clientIds,String locationIds) {
		PdfWriter writer;
		float[] columnWidths = { 50f, 100f, 50f, 50f, 50f, 50f,50f, 100f, 75f };
	    BooleanExpression mandatoryPredicate = QFleet.fleet.clients.any().uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		}
		

		if (locationIds != null && !locationIds.isBlank()) {
		    List<String> locationIdsList = Arrays.asList(locationIds.split(","));
		    BooleanExpression locationFilter = QFleet.fleet.spot.location.uuid.in(locationIdsList);

		    predicate = ExpressionUtils.allOf(predicate, locationFilter);
		}

		var fleets = fleetRepository.findAll(predicate);

		List<FleetExportDto> fleetExportDtos = new ArrayList<>();
		fleets.forEach(fleet -> fleetExportDtos.add(fleetMapper.mapToExportDto(fleet, timeZone)));
		try {

			Document document = new Document();
			writer = PdfWriter.getInstance(document, new FileOutputStream("iTextTable.pdf"));
			document.open();
			PdfContentByte pdfContentByte = writer.getDirectContent();
			uploadJobPdfLogo(document, "classpath:trailer-audit-pdf-images/Picture3.png", 220f, 729f, 130f, 35f);	
			String latoFontPath = "/Font/Lato-Regular.ttf";
			InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
			if (fontStream == null) {
				throw new IOException("Font file not found");
			}
			
			BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
			com.itextpdf.text.Font blackFont = new com.itextpdf.text.Font(bf, 8, com.itextpdf.text.Font.NORMAL, BaseColor.BLACK);
			ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER, new Phrase("ASSET INVENTORY REPORT"), 285, 720, 0);
			
			
			if (fleetExportDtos.size() > 60) {
				List<FleetExportDto> firstPageEntries = fleetExportDtos.subList(0, 60);
				List<FleetExportDto> remainingPageEntries = fleetExportDtos.subList(61, fleetExportDtos.size());

				PdfPTable table = modifyFleetTableInPdf(columnWidths, firstPageEntries, true);
				table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());

				List<List<FleetExportDto>> paginatedRows = fleetPaginate(remainingPageEntries, 74);
				for (List<FleetExportDto> pageRows : paginatedRows) {
					document.newPage();
					PdfPTable nextTable = modifyFleetTableInPdf(columnWidths, pageRows, false);
					document.add(nextTable);

				}

			} else {
				PdfPTable table = modifyFleetTableInPdf(columnWidths, fleetExportDtos, true);
				table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());

			}

			document.close();
			File pdfFile = new File("iTextTable.pdf");
			return new InputStreamResource(new FileInputStream(pdfFile));

		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.FLEET_EXPORT, e.getMessage());
		}
	}


	@Override
	@Transactional(readOnly = true)
	public Resource exportFleetEntryExitsAsCSV(Predicate predicate, String timeZone,
											   List<String> clientIds) {
//		log.info(">> exportFleetEntryExitsAsCSV({}, {}, {})", predicate, timeZone, clientIds);

		BooleanExpression mandatoryPredicate =
			QGuardEntryExit.guardEntryExit.location.client.uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		}

		var fleetEntryExits = guardEntryExitRepository.findAll(predicate);

		List<GuardEntryExitExportDto> entryExitExportDtos = new ArrayList<>();
		fleetEntryExits.forEach(fleetEntryExit -> entryExitExportDtos
			.add(guardEntryExitMapper.mapToExportDto(fleetEntryExit, timeZone, cdn)));

		try {
			Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
			File tmpFolder = tmpDirPath.toFile();
			String fileName =
				tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".csv";
			try (OutputStream outputStream = new FileOutputStream(fileName);) {
				String csvData = CSVUtils.toCSV(entryExitExportDtos, ',', true);
				outputStream.write(csvData.getBytes());
				outputStream.flush();
				return new InputStreamResource(new FileInputStream(fileName));
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting CSV!", e);
			throw new ServiceException(ErrorCode.FLEET_ENTRY_EXIT_EXPORT, e.getMessage());
		}
	}


	@Override
	@Transactional(readOnly = true)
	public Resource exportFleetEntryExitsAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds, String fromDate, String toDate) {
		// TODO Auto-generated method stub
//		log.info(">> exportFleetEntryExitsAsCSV({}, {}, {})", predicate, timeZone, clientIds);

		BooleanExpression mandatoryPredicate =
			QGuardEntryExit.guardEntryExit.location.client.uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		}
		
		if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
		      ZonedDateTime fromDateTime =
		          DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
		              .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
		      ZonedDateTime toDateTime =
		          DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
		              .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
		      BooleanExpression betweenDateCriteria =
		          QGuardEntryExit.guardEntryExit.createdDate.between(fromDateTime, toDateTime);
		      predicate =
		          Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
		    }

		var fleetEntryExits = guardEntryExitRepository.findAll(predicate);

		List<GuardEntryExitExportDto> entryExitExportDtos = new ArrayList<>();
		fleetEntryExits.forEach(fleetEntryExit -> entryExitExportDtos
			.add(guardEntryExitMapper.mapToExportDto(fleetEntryExit, timeZone, cdn)));

		try {


//	    	Workbook workbook = new XSSFWorkbook();
//	        Sheet sheet = workbook.createSheet("FleetEntryExit");
//	        Row headerRow = sheet.createRow(0);
//	        headerRow.createCell(0).setCellValue("guardEntryExitId");
//	        headerRow.createCell(1).setCellValue("locationName");
//	        headerRow.createCell(2).setCellValue("fleetCarrier");
//	        headerRow.createCell(3).setCellValue("fleetType");
//	        headerRow.createCell(4).setCellValue("fleetUnitNumber");
//	        headerRow.createCell(5).setCellValue("type");
//	        headerRow.createCell(6).setCellValue("notes");
//	        headerRow.createCell(7).setCellValue("createdDate");
//	        headerRow.createCell(8).setCellValue("lastModifiedDate");
//	        headerRow.createCell(9).setCellValue("createdBy");
//	        headerRow.createCell(10).setCellValue("lastModifiedBy");
			Workbook workbook = new XSSFWorkbook();
			Sheet sheet = workbook.createSheet("FleetEntryExit");

			// Create header row
			Row headerRow = sheet.createRow(0);

			Font boldFont = workbook.createFont();
			boldFont.setBold(true);
			boldFont.setColor(IndexedColors.WHITE.getIndex());
			CellStyle cellStyle = workbook.createCellStyle();
			cellStyle.setFont(boldFont);
			cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
			cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

			// Create cells with bold values
			Cell cell1 = headerRow.createCell(0);
			cell1.setCellValue("Guard Entry Exit Id");
			cell1.setCellStyle(cellStyle);

			Cell cell2 = headerRow.createCell(1);
			cell2.setCellValue("Location Name");
			cell2.setCellStyle(cellStyle);

			Cell cell3 = headerRow.createCell(2);
			cell3.setCellValue("Fleet Carrier");
			cell3.setCellStyle(cellStyle);

			Cell cell4 = headerRow.createCell(3);
			cell4.setCellValue("Fleet Type");
			cell4.setCellStyle(cellStyle);

			Cell cell5 = headerRow.createCell(4);
			cell5.setCellValue("Fleet UnitNumber");
			cell5.setCellStyle(cellStyle);

			Cell cell6 = headerRow.createCell(5);
			cell6.setCellValue("Type");
			cell6.setCellStyle(cellStyle);

//			Cell cell7 = headerRow.createCell(6);
//			cell7.setCellValue("Notes");
//			cell7.setCellStyle(cellStyle);
			
			Cell cell7 = headerRow.createCell(6);
			cell7.setCellValue("Spot");
			cell7.setCellStyle(cellStyle);
			
			Cell cell8 = headerRow.createCell(7);
			cell8.setCellValue("Pro Number");
			cell8.setCellStyle(cellStyle);
			
			Cell cell9 = headerRow.createCell(8);
			cell9.setCellValue("Driver Name");
			cell9.setCellStyle(cellStyle);

			Cell cell10 = headerRow.createCell(9);
			cell10.setCellValue("Bill of Lading");
			cell10.setCellStyle(cellStyle);
			
			Cell cell11 = headerRow.createCell(10);
			cell11.setCellValue("Carrier");
			cell11.setCellStyle(cellStyle);
			
			Cell cell12 = headerRow.createCell(11);
			cell12.setCellValue("Suppliers");
			cell12.setCellStyle(cellStyle);
			
			Cell cell13 = headerRow.createCell(12);
			cell13.setCellValue("Sub");
			cell13.setCellStyle(cellStyle);
			
			Cell cell14 = headerRow.createCell(13);
			cell14.setCellValue("Date of Pickup");
			cell14.setCellStyle(cellStyle);
			
			Cell cell15 = headerRow.createCell(14);
			cell15.setCellValue("Load Status");
			cell15.setCellStyle(cellStyle);
			
			Cell cell16 = headerRow.createCell(15);
			cell16.setCellValue("Due of Plant");
			cell16.setCellStyle(cellStyle);
			
			Cell cell17 = headerRow.createCell(16);
			cell17.setCellValue("Sequence Number");
			cell17.setCellStyle(cellStyle);
			
			Cell cell18 = headerRow.createCell(17);
			cell18.setCellValue("Date of Arrival");
			cell18.setCellStyle(cellStyle);
			
			Cell cell19 = headerRow.createCell(18);
			cell19.setCellValue("Tractor Number");
			cell19.setCellStyle(cellStyle);
			
			Cell cell20 = headerRow.createCell(19);
			cell20.setCellValue("Created Date");
			cell20.setCellStyle(cellStyle);

			Cell cell21 = headerRow.createCell(20);
			cell21.setCellValue("Last Modified Date");
			cell21.setCellStyle(cellStyle);

			Cell cell22 = headerRow.createCell(21);
			cell22.setCellValue("Created By");
			cell22.setCellStyle(cellStyle);

			Cell cell23 = headerRow.createCell(22);
			cell23.setCellValue("Last Modified By");
			cell23.setCellStyle(cellStyle);
			
			Cell cell24 = headerRow.createCell(23);
			cell24.setCellValue("Bill Of Lading Image");
			cell24.setCellStyle(cellStyle);
			int rowNum = 1;
			for (GuardEntryExitExportDto entryExitExportDto : entryExitExportDtos) {
				Row row = sheet.createRow(rowNum++);
				row.createCell(0).setCellValue(entryExitExportDto.getGuardEntryExitId());
				row.createCell(1).setCellValue(entryExitExportDto.getLocationName());
				row.createCell(2).setCellValue(entryExitExportDto.getFleetCarrier());
				row.createCell(3).setCellValue(entryExitExportDto.getFleetType().toString());
				row.createCell(4).setCellValue(entryExitExportDto.getFleetUnitNumber());
				row.createCell(5).setCellValue(entryExitExportDto.getType().toString());
//				row.createCell(6).setCellValue(entryExitExportDto.getNotes());
				row.createCell(6).setCellValue(entryExitExportDto.getSpotName());
				row.createCell(7).setCellValue(entryExitExportDto.getProNumber());
				row.createCell(8).setCellValue(entryExitExportDto.getDriverName());
				row.createCell(9).setCellValue(entryExitExportDto.getBillOfLading());
				row.createCell(10).setCellValue(entryExitExportDto.getCarrier());
				row.createCell(11).setCellValue(entryExitExportDto.getSupplier());
				row.createCell(12).setCellValue(entryExitExportDto.getSub());
				row.createCell(13).setCellValue(entryExitExportDto.getDateOfPickup());
				row.createCell(14).setCellValue(entryExitExportDto.getLoadStatus());
				row.createCell(15).setCellValue(entryExitExportDto.getDueAtPlant());
				row.createCell(16).setCellValue(entryExitExportDto.getSequenceNumber());
				row.createCell(17).setCellValue(entryExitExportDto.getDateOfArrival());
				row.createCell(18).setCellValue(entryExitExportDto.getTractorNumber());
				row.createCell(19).setCellValue(entryExitExportDto.getCreatedDate());
				row.createCell(20).setCellValue(entryExitExportDto.getLastModifiedDate());
				row.createCell(21).setCellValue(entryExitExportDto.getCreatedBy());
				row.createCell(22).setCellValue(entryExitExportDto.getLastModifiedBy());
				
				 String imageUrl = entryExitExportDto.getBillOfLandingImage();
		            if (imageUrl != null && !imageUrl.isEmpty()) {
		                // Download image from URL or use an existing image path (like from CDN)
		                byte[] imageBytes = downloadImage(imageUrl);
		                
		                if (imageBytes != null) {
		                    // Add the image to the workbook
		                    int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);
		                    // Create a drawing layer
		                    Drawing<?> drawing = sheet.createDrawingPatriarch();
		                    // Create an anchor for the image
		                    ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
		                    anchor.setCol1(23);  // Set the column 
		                    anchor.setRow1(rowNum);  // Set the row number
		                    
		                    // Create a picture and link it to the anchor
		                    Picture picture = drawing.createPicture(anchor, pictureIdx);
		                    picture.resize(0.9);  // Resize the image to fit the cell
		                    
		                    row.setHeightInPoints(40);  // Increase row height
		                    sheet.setColumnWidth(23, 8000); 
		                }
		            }
			}
			Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
			File tmpFolder = tmpDirPath.toFile();
			String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
			try (OutputStream outputStream = new FileOutputStream(fileName)) {
				workbook.write(outputStream);
				return new InputStreamResource(new FileInputStream(fileName));
			}

		} catch (Exception e) {
			log.error("Error occurred while exporting EXCEL!", e);
			throw new ServiceException(ErrorCode.FLEET_ENTRY_EXIT_EXPORT, e.getMessage());
		}


	}
	
	
	@Transactional(readOnly = true)
	public Resource exportEntryExitsReportAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds, String fromDate, String toDate) {
		// TODO Auto-generated method stub
//		log.info(">> exportFleetEntryExitsAsCSV({}, {}, {})", predicate, timeZone, clientIds);

		BooleanExpression mandatoryPredicate =
			QGuardEntryExit.guardEntryExit.location.client.uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		}
		
		if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
		      ZonedDateTime fromDateTime =
		          DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
		              .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
		      ZonedDateTime toDateTime =
		          DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
		              .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
		      BooleanExpression betweenDateCriteria =
		          QGuardEntryExit.guardEntryExit.createdDate.between(fromDateTime, toDateTime);
		      predicate =
		          Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
		    }

		var fleetEntryExits = guardEntryExitRepository.findAll(predicate);

		List<GuardEntryExitExportDto> entryExitExportDtos = new ArrayList<>();
		fleetEntryExits.forEach(fleetEntryExit -> entryExitExportDtos
			.add(guardEntryExitMapper.mapToExportDto(fleetEntryExit, timeZone, cdn)));

		try {

			Workbook workbook = new XSSFWorkbook();
			Sheet sheet = workbook.createSheet("FleetEntryExit");

			// Create header row
			Row headerRow = sheet.createRow(0);

			Font boldFont = workbook.createFont();
			boldFont.setBold(true);
			boldFont.setColor(IndexedColors.WHITE.getIndex());
			CellStyle cellStyle = workbook.createCellStyle();
			cellStyle.setFont(boldFont);
			cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
			cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

			// Create cells with bold values
			Cell cell1 = headerRow.createCell(0);
			cell1.setCellValue("Trailer #");
			cell1.setCellStyle(cellStyle);

			Cell cell2 = headerRow.createCell(1);
			cell2.setCellValue("Location");
			cell2.setCellStyle(cellStyle);

			Cell cell3 = headerRow.createCell(2);
			cell3.setCellValue("Tractor #");
			cell3.setCellStyle(cellStyle);

			Cell cell4 = headerRow.createCell(3);
			cell4.setCellValue("Carrier");
			cell4.setCellStyle(cellStyle);

			Cell cell5 = headerRow.createCell(4);
			cell5.setCellValue("Supplier");
			cell5.setCellStyle(cellStyle);

			Cell cell6 = headerRow.createCell(5);
			cell6.setCellValue("Sequence #");
			cell6.setCellStyle(cellStyle);
			
			Cell cell7 = headerRow.createCell(6);
			cell7.setCellValue("Load Status");
			cell7.setCellStyle(cellStyle);
			
			Cell cell8 = headerRow.createCell(7);
			cell8.setCellValue("Arrival Date");
			cell8.setCellStyle(cellStyle);
			
			Cell cell9 = headerRow.createCell(8);
			cell9.setCellValue("Pickup Date");
			cell9.setCellStyle(cellStyle);

			Cell cell10 = headerRow.createCell(9);
			cell10.setCellValue("Driver Name");
			cell10.setCellStyle(cellStyle);
			
			
			int rowNum = 1;
			for (GuardEntryExitExportDto entryExitExportDto : entryExitExportDtos) {
				Row row = sheet.createRow(rowNum++);
				row.createCell(0).setCellValue(entryExitExportDto.getFleetUnitNumber());
				if(entryExitExportDto.getSpotName() != null) {
					row.createCell(1).setCellValue(entryExitExportDto.getLocationName() + "  " + entryExitExportDto.getSpotName());
				}else {
					row.createCell(1).setCellValue(entryExitExportDto.getLocationName());
				}
				
				row.createCell(2).setCellValue(entryExitExportDto.getTractorNumber());
				row.createCell(3).setCellValue(entryExitExportDto.getCarrier());
				row.createCell(4).setCellValue(entryExitExportDto.getSupplier());
				row.createCell(5).setCellValue(entryExitExportDto.getSequenceNumber());
				row.createCell(6).setCellValue(entryExitExportDto.getLoadStatus());
				row.createCell(7).setCellValue(entryExitExportDto.getDateOfArrival());
				row.createCell(8).setCellValue(entryExitExportDto.getDateOfPickup());
				row.createCell(9).setCellValue(entryExitExportDto.getDriverName());
				
				 String imageUrl = entryExitExportDto.getBillOfLandingImage();
		            if (imageUrl != null && !imageUrl.isEmpty()) {
		                // Download image from URL or use an existing image path (like from CDN)
		                byte[] imageBytes = downloadImage(imageUrl);
		                
		                if (imageBytes != null) {
		                    // Add the image to the workbook
		                    int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);
		                    // Create a drawing layer
		                    Drawing<?> drawing = sheet.createDrawingPatriarch();
		                    // Create an anchor for the image
		                    ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
		                    anchor.setCol1(23);  // Set the column 
		                    anchor.setRow1(rowNum);  // Set the row number
		                    
		                    // Create a picture and link it to the anchor
		                    Picture picture = drawing.createPicture(anchor, pictureIdx);
		                    picture.resize(0.9);  // Resize the image to fit the cell
		                    
		                    row.setHeightInPoints(40);  // Increase row height
		                    sheet.setColumnWidth(23, 8000); 
		                }
		            }
			}
			Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
			File tmpFolder = tmpDirPath.toFile();
			String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
			try (OutputStream outputStream = new FileOutputStream(fileName)) {
				workbook.write(outputStream);
				return new InputStreamResource(new FileInputStream(fileName));
			}

		} catch (Exception e) {
			log.error("Error occurred while exporting EXCEL!", e);
			throw new ServiceException(ErrorCode.FLEET_ENTRY_EXIT_EXPORT, e.getMessage());
		}


	}
	
	
	
	@Transactional(readOnly = true)
	public Resource exportEntryExitsReportAsPDF(Predicate predicate, String timeZone, List<String> clientIds, String fromDate, String toDate) {
		// TODO Auto-generated method stub
//		log.info(">> exportFleetEntryExitsAsCSV({}, {}, {})", predicate, timeZone, clientIds);
		PdfWriter writer;
		float[] columnWidths = { 50f, 100f, 50f, 50f, 50f, 75f, 75f, 75f , 75f, 75f };
		SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
	    SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
		String startDate = "";
		String endDate = "";
	    
	    try
	    {
	    	if(fromDate != null)
	    	{
	    		Date date1 = inputFormat.parse(fromDate);
	       	    startDate = outputFormat.format(date1);
	    	}
	    	 
	    	 if(toDate != null)
	    	 {
	    		Date date2 = inputFormat.parse(toDate);
	        	endDate = outputFormat.format(date2);
	    	 }	
	    }
	    catch (ParseException e) {
	        e.printStackTrace();
	    }
		
		BooleanExpression mandatoryPredicate =
			QGuardEntryExit.guardEntryExit.location.client.uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		}
		
		if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
		      ZonedDateTime fromDateTime =
		          DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
		              .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
		      ZonedDateTime toDateTime =
		          DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
		              .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
		      BooleanExpression betweenDateCriteria =
		          QGuardEntryExit.guardEntryExit.createdDate.between(fromDateTime, toDateTime);
		      predicate =
		          Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
		    }

		var fleetEntryExits = guardEntryExitRepository.findAll(predicate);

		List<GuardEntryExitExportDto> entryExitExportDtos = new ArrayList<>();
		fleetEntryExits.forEach(fleetEntryExit -> entryExitExportDtos
			.add(guardEntryExitMapper.mapToExportDto(fleetEntryExit, timeZone, cdn)));
		
		
		try {

			Document document = new Document();
			writer = PdfWriter.getInstance(document, new FileOutputStream("iTextTable.pdf"));
			document.open();
			PdfContentByte pdfContentByte = writer.getDirectContent();
			uploadJobPdfLogo(document, "classpath:trailer-audit-pdf-images/Picture3.png", 220f, 729f, 130f, 35f);	
			String latoFontPath = "/Font/Lato-Regular.ttf";
			InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
			if (fontStream == null) {
				throw new IOException("Font file not found");
			}
			
			BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
			com.itextpdf.text.Font blackFont = new com.itextpdf.text.Font(bf, 8, com.itextpdf.text.Font.NORMAL, BaseColor.BLACK);
			ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER, new Phrase("ENTRY EXIT REPORT"), 285, 720, 0);
			if(Objects.nonNull(fromDate) && Objects.nonNull(toDate)) {
				ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER, new Phrase(startDate +" to "+ endDate, blackFont), 290, 700, 0);
			}
			
			
			if (entryExitExportDtos.size() > 60) {
				List<GuardEntryExitExportDto> firstPageEntries = entryExitExportDtos.subList(0, 60);
				List<GuardEntryExitExportDto> remainingPageEntries = entryExitExportDtos.subList(61, entryExitExportDtos.size());

				PdfPTable table = modifyTableInPdf(columnWidths, firstPageEntries, true);
				table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());

				List<List<GuardEntryExitExportDto>> paginatedRows = paginate(remainingPageEntries, 74);
				for (List<GuardEntryExitExportDto> pageRows : paginatedRows) {
					document.newPage();
					PdfPTable nextTable = modifyTableInPdf(columnWidths, pageRows, false);
					document.add(nextTable);

				}

			} else {
				PdfPTable table = modifyTableInPdf(columnWidths, entryExitExportDtos, true);
				table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());

			}

			document.close();
			File pdfFile = new File("iTextTable.pdf");
			return new InputStreamResource(new FileInputStream(pdfFile));

		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
		
	}
	
	

	@Override
	@Transactional
	public boolean mergeFleet(FleetMergeDto fleetMergeDto) {
		boolean isMerged = true;
		Set<Fleet> unwantedFleets = new HashSet<>();
		String mergedFleets = String.join(",", fleetMergeDto.getUnitNumberList());
		List<String> unitNumbers = fleetMergeDto.getUnitNumberList();
		Fleet fleet;
		fleet = fleetRepository.findByUnitNumber(fleetMergeDto.getSelectedUnitNumber());
		DuplicateTrailers duplicateTrailer1 = new DuplicateTrailers();
		duplicateTrailer1.setUnitNumber(fleet.getUnitNumber());
		duplicateTrailersRepository.save(duplicateTrailer1);
		if (fleetMergeDto.getAssignedUnitNumber() != null) {

			fleet.setUnitNumber(fleetMergeDto.getAssignedUnitNumber());
			List<Spot> spots1 = spotRepository.findAllByFleet(fleet);
			if (!(spots1.isEmpty())) {
				for (Spot spot : spots1) {

//  				spot.setFleet(fleet);
					spot.setFleet(null);
					spot.setStatus(Status.EMPTY);
					spotRepository.save(spot);
				}
			}

		}
		unitNumbers.remove(fleetMergeDto.getSelectedUnitNumber());
//		if (fleetMergeDto.getCarrier() != null) {
//			Carriers carrier = carrierRepository.findByUuid(fleetMergeDto.getCarrier())
//		            .orElseThrow(() -> new ServiceException(ErrorCode.CARRIER_NOT_FOUND,
//		            		fleetMergeDto.getCarrier()));
//			fleet.setCarriers(carrier);
//		}
		if (fleetMergeDto.getCarrier() != null)
			fleet.setCarrier(fleetMergeDto.getCarrier());
		if (fleetMergeDto.getOwner() != null)
			fleet.setOwner(fleetMergeDto.getOwner());
		if (fleetMergeDto.getType() != null)
			fleet.setType(fleetMergeDto.getType());
		UserAuthDto userauthdto = AuthDetailsProvider.getLoggedInUser();
		User user = usermapper.mapToUser(userauthdto);
		fleet.setCreatedBy(user);
		fleet.setLastModifiedBy(user);

		fleetRepository.save(fleet);
//  		if(fleetMergeDto.getClient()!=null)
//  		 {
//
//
//		fleetIds1.add(fleet.getId());
//		List<Client> clientses1=clientRepository.findAllByFleetIds(fleetIds1);
//		if(!(clientses1.isEmpty()))
//		{
//
//		   for(Client client:clientses1)
//		  {
//			   log.info("client>>>><<<<<{}",client.getClientName());
//			 Set<Fleet> fleetsy= client.getFleets();
//			 fleetsy.remove(fleet);
//			 client.setFleets(fleetsy);
//			  clientRepository.save(client);
//		  }
//		}
//
//	Client client1=new Client();
//    Set<Fleet> fleets1=new HashSet<>();
//    Set<Client> clients1=new HashSet<>();
//    List<Client> updatedClients=new ArrayList<>();
//    fleets1.add(fleet);
//    log.info("clientId>>>>>><<<>>>{}",fleetMergeDto.getClient());
//       client1=clientRepository.findUsingUuid(fleetMergeDto.getClient());
//       fleets1=client1.getFleets();
//       fleets1.add(fleet);
//       client1.setFleets(fleets1);
//       clientRepository.save(client1);
//}
		Fleet fleet1;
		Set<Long> fleetIds = new HashSet<>();
		for (String unitNumber1 : unitNumbers) {
			fleet1 = fleetRepository.findByUnitNumber(unitNumber1);
			fleetIds.add(fleet1.getId());
			List<Client> clientses = clientRepository.findAllByFleetIds(fleetIds);
			if (!(clientses.isEmpty())) {
				for (Client client : clientses) {
					Set<Fleet> fleets = client.getFleets();
					fleets.remove(fleet1);
//  			 client.setFleets(fleets);
//  			  clientRepository.save(client);

				}
			}
			List<GuardEntryExit> guardEntryExits = guardEntryExitRepository.findAllLatestByFleetIds(fleetIds);
			if (!(guardEntryExits.isEmpty())) {
				for (GuardEntryExit guardEntryExit : guardEntryExits) {
					guardEntryExit.setFleet(fleet);
					guardEntryExitRepository.save(guardEntryExit);
				}
			}
			List<Message> messages = messagerepository.findAllLatestByFleetIds(fleetIds);
			if (!(messages.isEmpty())) {
				for (Message message : messages) {
					message.setFleet(fleet);
					messagerepository.save(message);
				}
			}
			List<Job> jobs = jobRepository.findAllLatestByFleetIds(fleetIds);
			if (!(jobs.isEmpty())) {
				for (Job job : jobs) {
					job.setFleet(fleet);
					jobRepository.save(job);
				}
			}
			List<Spot> spots = spotRepository.findAllByFleet(fleet1);
			if (!(spots.isEmpty())) {
				for (Spot spot : spots) {

//  				spot.setFleet(fleet);
					spot.setFleet(null);
					spot.setStatus(Status.EMPTY);
					spotRepository.save(spot);
				}
			}
			List<TrailerAudit> trailerAudits = trailerAuditRepository.findAllLatestByFleetIds(fleetIds);
			if (!(trailerAudits.isEmpty())) {
				for (TrailerAudit trailerAudit : trailerAudits) {
					trailerAudit.setFleet(fleet);
					trailerAuditRepository.save(trailerAudit);
				}
			}

			fleetIds.clear();
			unwantedFleets.add(fleet1);
		}
		for (Fleet fleetDeleted : unwantedFleets) {
			List<TrailerLog> logs = trailerLogRepository.findByFleetId(fleetDeleted.getId());
			for(TrailerLog log : logs) {
				if(!log.getFleet().getUuid().isEmpty()) {
					log.setFleet(fleet);
				}
			}
			DuplicateTrailers duplicateTrailer = new DuplicateTrailers();
			duplicateTrailer.setUnitNumber(fleetDeleted.getUnitNumber());
			duplicateTrailersRepository.save(duplicateTrailer);
		}

		fleetRepository.deleteAll(unwantedFleets);// ISSUE IS IN THIS LINE
		
		// Trailer activities logged
		TrailerLog log = new TrailerLog();
		log.setActions("Merged trailers ("+ mergedFleets + ") to " + fleet.getUnitNumber());
		log.setFleet(fleet);
		log.setIsActive(true);
		trailerLogRepository.save(log);
		
		return isMerged;

//  	catch (ConstraintViolationException | DataIntegrityViolationException e) {
//  	    log.error("Error occurred while creating fleet! Reason : {}", e.getMessage());
//  	    throw new ServiceException(ErrorCode.DUPLICATE_FLEET_UNIT_NUMBER);
//  	  }
	}

	@Override
	@Transactional
	public List<String> getUniqueCarrier(String carrier) {
		List<String> fleetCarriers = fleetRepository.findUniqueCarrier(carrier);
		List<String> guardExitCarriers = fleetRepository.findUniqueCarrierGuardEntryExit(carrier);
		
		Set<String> uniqueCarriers = new HashSet<>();
		uniqueCarriers.addAll(fleetCarriers);
		uniqueCarriers.addAll(guardExitCarriers);
		    
		return new ArrayList<>(uniqueCarriers);
	}

	@Override
	public List<String> getUniqueCarrier() {
		List<String> fleetCarriers = fleetRepository.findUniqueCarrier();
	    List<String> guardExitCarriers = fleetRepository.findUniqueCarrierGuardEntryExit();

	    
	    Set<String> uniqueCarriers = new HashSet<>();
	    
	    
	    uniqueCarriers.addAll(fleetCarriers.stream()
	        .filter(carrierName -> carrierName != null && !carrierName.trim().isEmpty())
	        .collect(Collectors.toList()));

	    uniqueCarriers.addAll(guardExitCarriers.stream()
	        .filter(carrierName -> carrierName != null && !carrierName.trim().isEmpty())
	        .collect(Collectors.toList()));

	    
	    return new ArrayList<>(uniqueCarriers);
	}
	
	@Override
	@Transactional
    public Page<String> getUniqueCarrierPage(String carrier, Pageable pageable) {
//        return fleetRepository.findUniqueCarrierPage(carrier != null ? carrier : "", pageable);
		List<String> fleetCarriers = fleetRepository.findUniqueCarrier();
	    List<String> guardExitCarriers = fleetRepository.findUniqueCarrierGuardEntryExit();

	    
	    Set<String> uniqueCarriers = new HashSet<>();
	    
	    
	    uniqueCarriers.addAll(fleetCarriers.stream()
	        .filter(carrierName -> carrierName != null && !carrierName.trim().isEmpty())
	        .collect(Collectors.toList()));

	    uniqueCarriers.addAll(guardExitCarriers.stream()
	        .filter(carrierName -> carrierName != null && !carrierName.trim().isEmpty())
	        .collect(Collectors.toList()));
	    
	    List<String> combinedList = new ArrayList<>(uniqueCarriers);

		   
		    int totalElements = combinedList.size();
		    int totalPages = (int) Math.ceil((double) totalElements / pageable.getPageSize());
		    int start = Math.min((int) pageable.getOffset(), totalElements);
		    int end = Math.min((start + pageable.getPageSize()), totalElements);
		    List<String> paginatedCarriers = combinedList.subList(start, end);

		    
		    return new PageImpl<>(paginatedCarriers, pageable, totalElements);
    }

	@Override
	@Transactional
	public int updateCarrierInFleet(FleetCarrierUpdate fleetCarrierUpdate) {
		List<String> renameCarriers = fleetCarrierUpdate.getRenameCarriers();
//		renameCarriers.forEach(item -> {
//			Carriers carrier = carrierRepository.findByUuid(item)
//					.orElseThrow(() -> new ServiceException(ErrorCode.FLEET_NOT_FOUND, item));
////			fleetRepository.updateCarrierByMerging(carrier);
//		});
//		Carriers savedCarrier = new Carriers();
//		var carrier = fleetCarrierUpdate.getCarrier();
//		if (StringUtils.isNotBlank(fleetCarrierUpdate.getNewCarrier())) {
//			 log.error("\\\\\\\\\\\\\\\\\\\\\\\\\\\\carrier! Reason : {}", fleetCarrierUpdate.getNewCarrier());
//			savedCarrier.setCarrier(fleetCarrierUpdate.getNewCarrier());
//			savedCarrier = carrierRepository.save(savedCarrier);
//			carrier = savedCarrier.getUuid();
//		}
//		
//		return fleetRepository.updateFleetByCarrier(carrier, renameCarriers);
		return fleetRepository.updateFleetByCarrier(fleetCarrierUpdate.getCarrier(), renameCarriers);

	}
	
	private byte[] downloadImage(String imageUrl) throws IOException {
	    // Download image from the URL
	    try (InputStream inputStream = new URL(imageUrl).openStream()) {
	        return IOUtils.toByteArray(inputStream);
	    }
	}
	
	
	private void uploadJobPdfLogo(Document document, String fileName, float imageHorizontalPosition,
			float imageVerticalPosition, float imageWidth, float imageHeight) throws IOException, DocumentException {
		Image image = Image.getInstance(fileName);
		image.setAbsolutePosition(imageHorizontalPosition, imageVerticalPosition);
		image.scaleAbsoluteWidth(imageWidth);
		image.scaleAbsoluteHeight(imageHeight);
		document.add(image);
	}
	
	private PdfPTable modifyTableInPdf(float[] columnWidths, List<GuardEntryExitExportDto> entryExitExportData, Boolean isFirstPage) {
		PdfPTable table = new PdfPTable(columnWidths);
		if (isFirstPage == true) {
			addTableHeader(table);
		}
		addRows(table, entryExitExportData);
		table.setTotalWidth(500);

		table.setLockedWidth(true);

		return table;
	}
	
	private PdfPTable modifyFleetTableInPdf(float[] columnWidths, List<FleetExportDto> fleetExportData, Boolean isFirstPage) {
		PdfPTable table = new PdfPTable(columnWidths);
		if (isFirstPage == true) {
			addFleetTableHeader(table);
		}
		addFleetRows(table, fleetExportData);
		table.setTotalWidth(500);

		table.setLockedWidth(true);

		return table;
	}
	
	private void addTableHeader(PdfPTable table) {
		try {
			String latoFontPath = "/Font/Lato-SemiBold.ttf";
			InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
			if (fontStream == null) {
				throw new IOException("File not found");
			}
			BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED,
					fontStream.readAllBytes(), null);
			com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL,
					BaseColor.BLACK);
			Stream.of("Trailer #", "Location", "Tractor #", "Carrier", "Supplier", "Sequence #", "Load Status", "Arrival Date", "Pickup Date", "Driver Name")
					.forEach(columnTitle -> {
						PdfPCell header = new PdfPCell();
						header.setBorderColor(new BaseColor(221, 221, 223));
						header.setPhrase(new Phrase(columnTitle, font));
						header.setHorizontalAlignment(Element.ALIGN_LEFT);
						header.setVerticalAlignment(Element.ALIGN_MIDDLE);
						header.setFixedHeight(20f);
						table.addCell(header);
					});

			for (int i = 0; i < 10; i++) {
				PdfPCell borderCell = new PdfPCell();
				borderCell.setBackgroundColor(new BaseColor(205, 205, 205));
				borderCell.setBorderColor(new BaseColor(221, 221, 223));
				borderCell.setFixedHeight(5);
				table.addCell(borderCell);
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
	}
	
	private void addFleetTableHeader(PdfPTable table) {
		try {
			String latoFontPath = "/Font/Lato-SemiBold.ttf";
			InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
			if (fontStream == null) {
				throw new IOException("File not found");
			}
			BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED,
					fontStream.readAllBytes(), null);
			com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL,
					BaseColor.BLACK);
			Stream.of("Type", "Unit Number", "Carrier", "Trailer Status", "Location", "Sequence Number", "Commodity", "Notes", "Created By")
					.forEach(columnTitle -> {
						PdfPCell header = new PdfPCell();
						header.setBorderColor(new BaseColor(221, 221, 223));
						header.setPhrase(new Phrase(columnTitle, font));
						header.setHorizontalAlignment(Element.ALIGN_LEFT);
						header.setVerticalAlignment(Element.ALIGN_MIDDLE);
						header.setFixedHeight(20f);
						table.addCell(header);
					});

			for (int i = 0; i < 9; i++) {
				PdfPCell borderCell = new PdfPCell();
				borderCell.setBackgroundColor(new BaseColor(205, 205, 205));
				borderCell.setBorderColor(new BaseColor(221, 221, 223));
				borderCell.setFixedHeight(5);
				table.addCell(borderCell);
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
	}
	
	private void addFleetTableHeader(PdfPTable table)
					fontStream.readAllBytes(), null);
			com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL,
					BaseColor.BLACK);
		
			Stream.of( "Type", "Unit Number","Carrier", "Trailer Status","Location","Sequence Number","Commodity", "Notes", "Created By" )
					.forEach(columnTitle -> {
						PdfPCell header = new PdfPCell();
						header.setBorderColor(new BaseColor(221, 221, 223));
						header.setPhrase(new Phrase(columnTitle, font));
						header.setHorizontalAlignment(Element.ALIGN_LEFT);
						header.setVerticalAlignment(Element.ALIGN_MIDDLE);
						header.setFixedHeight(20f);
						table.addCell(header);
					});

			for (int i = 0; i < 9; i++) {
				PdfPCell borderCell = new PdfPCell();
				borderCell.setBackgroundColor(new BaseColor(205, 205, 205));
				borderCell.setBorderColor(new BaseColor(221, 221, 223));
				borderCell.setFixedHeight(5);
				table.addCell(borderCell);
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
	}
	
	private void addRows(PdfPTable table, List<GuardEntryExitExportDto> entryExitExportDtos) {
		try {
			for (GuardEntryExitExportDto entryExitExportDto : entryExitExportDtos) {
				String latoFontPath = "/Font/Lato-Regular.ttf";
				InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
				if (fontStream == null) {
					// throw new IOException("Font file not found");
					throw new IOException("File not found");
				}
				BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED,
						BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
				com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL);
				addCellInPDFWithHeading(table, entryExitExportDto.getFleetUnitNumber(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				if(entryExitExportDto.getSpotName() != null) {
					addCellInPDFWithHeading(table, entryExitExportDto.getLocationName().concat("  ").concat(entryExitExportDto.getSpotName()), font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}else {
					addCellInPDFWithHeading(table, entryExitExportDto.getLocationName(), font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}
				
				addCellInPDFWithHeading(table, entryExitExportDto.getTractorNumber(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, entryExitExportDto.getCarrier(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, entryExitExportDto.getSupplier(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, entryExitExportDto.getSequenceNumber(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, entryExitExportDto.getLoadStatus(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, entryExitExportDto.getDateOfArrival(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, entryExitExportDto.getDateOfPickup(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, entryExitExportDto.getDriverName(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));

			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
	}
	
	private void addFleetRows(PdfPTable table, List<FleetExportDto> fleetExportDtos) {
		try {
			for (FleetExportDto fleettDto : fleetExportDtos) {
				String latoFontPath = "/Font/Lato-Regular.ttf";
				InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
				if (fontStream == null) {
					// throw new IOException("Font file not found");
					throw new IOException("File not found");
				}
				BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED,
						BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
				com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL);
				
				addCellInPDFWithHeading(table, fleettDto.getType().toString(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, fleettDto.getUnitNumber(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				addCellInPDFWithHeading(table, fleettDto.getCarrier(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				
				if (fleettDto.getFleetStatus() != null && fleettDto.getFleetStatus() == Job.FleetStatus.FULL) {
					addCellInPDFWithHeading(table, "LOADED", font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}else if(fleettDto.getFleetStatus() != null) {
					addCellInPDFWithHeading(table, fleettDto.getFleetStatus().toString(), font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}
				else {
					addCellInPDFWithHeading(table, "", font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}
				
				if (fleettDto.getSpot() != null) {
					addCellInPDFWithHeading(table, fleettDto.getSpot().getLocation().getLocationName()+" "+fleettDto.getSpot().getSpotName(), font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
//					addCellInPDFWithHeading(table, fleettDto.getSpot().getSpotName(), font, BaseColor.WHITE,
//							new BaseColor(221, 221, 223));
				}
				else {
					addCellInPDFWithHeading(table, "", font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}
				
				addCellInPDFWithHeading(table, fleettDto.getSequenceNumber(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				
				String commodityNames = fleettDto.getCommodity().stream()
					    .map(SuppliersDto::getSupplier) 
					    .collect(Collectors.joining(", "));
				
				addCellInPDFWithHeading(table, commodityNames, font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				
				addCellInPDFWithHeading(table, fleettDto.getRemarks(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				
//				if (fleettDto.getIsActive() == true) {
//					addCellInPDFWithHeading(table, "True", font, BaseColor.WHITE,
//							new BaseColor(221, 221, 223));
//				}else {
//					addCellInPDFWithHeading(table, "False", font, BaseColor.WHITE,
//							new BaseColor(221, 221, 223));
//				}
//				
//				addCellInPDFWithHeading(table, fleettDto.getOwner(), font, BaseColor.WHITE,
//						new BaseColor(221, 221, 223));
//				addCellInPDFWithHeading(table, fleettDto.getCreatedDate(), font, BaseColor.WHITE,
//						new BaseColor(221, 221, 223));
				
				addCellInPDFWithHeading(table, fleettDto.getCreatedBy(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
//				addCellInPDFWithHeading(table, fleettDto.getLastModifiedDate(), font, BaseColor.WHITE,
//						new BaseColor(221, 221, 223));
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
	}
	
	private static void addCellInPDFWithHeading(PdfPTable table, String heading, com.itextpdf.text.Font font,
			BaseColor backgroundColor, BaseColor borderColor) {
		PdfPCell cell = new PdfPCell(new Phrase(heading, font));
		cell.setBackgroundColor(backgroundColor);
		cell.setBorderColor(borderColor);
		cell.setMinimumHeight(10f); // Use minimum height instead of fixed height
		cell.setNoWrap(false); // Allow text wrapping
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		cell.setVerticalAlignment(Element.ALIGN_TOP); // Change to TOP for multi-line text
		table.addCell(cell);
	}
	
	private List<List<GuardEntryExitExportDto>> paginate(List<GuardEntryExitExportDto> rows, int rowsPerPage) {

		return Stream.iterate(0, i -> i + rowsPerPage).limit((long) Math.ceil((double) rows.size() / rowsPerPage))
				.map(i -> rows.subList(i, Math.min(i + rowsPerPage, rows.size()))).collect(Collectors.toList());
	}
	
	private List<List<FleetExportDto>> fleetPaginate(List<FleetExportDto> rows, int rowsPerPage) {

		return Stream.iterate(0, i -> i + rowsPerPage).limit((long) Math.ceil((double) rows.size() / rowsPerPage))
				.map(i -> rows.subList(i, Math.min(i + rowsPerPage, rows.size()))).collect(Collectors.toList());
	}

	@Override
	@Transactional(readOnly = true)
	public PagedResponse<TrailerLogDto> getTrailerLogs(Predicate predicate, Pageable pageable, String timeZone,
											 List<String> clientIds, String fromDate, String toDate) {
		BooleanExpression mandatoryPredicate = QTrailerLog.trailerLog.fleet.clients.any().uuid.in(clientIds);

		if (CollectionUtils.isNotEmpty(clientIds)) {
			if (Objects.isNull(predicate)) {
				predicate = mandatoryPredicate;
			} else {
				predicate = mandatoryPredicate.and(predicate);
			}
		} else {
			mandatoryPredicate = null;
		}
		
		if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
			ZonedDateTime fromDateTime = DateTimeUtils
					.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT).atTime(LocalTime.MIN)
					.atZone(ZoneId.of(timeZone));
			ZonedDateTime toDateTime = DateTimeUtils
					.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT).atTime(LocalTime.MAX)
					.atZone(ZoneId.of(timeZone));
			BooleanExpression betweenDateCriteria = QTrailerLog.trailerLog.createdDate.between(fromDateTime, toDateTime);
			predicate = Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
		}

		Page<TrailerLog> logsPage = trailerLogRepository.findAll(predicate, pageable);

		return PagedResponse.<TrailerLogDto>builder()
			.list(logsPage.stream().map(fleet -> trailerLogMapper.mapToDto(fleet, timeZone))
				.collect(Collectors.toList()))
			.page(logsPage.getNumber()).size(logsPage.getSize())
			.totalElements(logsPage.getTotalElements()).build();
	}
	
	@Override
	public Resource exportTrailerHistorysAsEXCEL(String fromDate, String toDate, Predicate predicate, String timeZone) {

		if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
			ZonedDateTime fromDateTime =
				DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
					.atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
			ZonedDateTime toDateTime =
				DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
					.atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
			BooleanExpression betweenDateCriteria =
				QTrailerLog.trailerLog.createdDate.between(fromDateTime, toDateTime);
			predicate =
				Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
		}

		var trailers = trailerLogRepository.findAll(predicate);
		List<TrailerLogExportDto> trailerLogExportDtos = new ArrayList<>();
		trailers.forEach(logs -> trailerLogExportDtos.add(trailerLogMapper.mapToExportDto(logs, timeZone)));

		try {
			Workbook workbook = new XSSFWorkbook();
			Sheet sheet = workbook.createSheet("Trailer History");

			// Create header row
			Row headerRow = sheet.createRow(0);
			Font boldFont = workbook.createFont();
			boldFont.setBold(true);
			boldFont.setColor(IndexedColors.WHITE.getIndex());
			CellStyle cellStyle = workbook.createCellStyle();
			cellStyle.setFont(boldFont);
			cellStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
			cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

			// Create header cells matching TrailerLogExportDto fields
			headerRow.createCell(0).setCellValue("CREATION DATE");
			headerRow.createCell(1).setCellValue("ACTION");
			headerRow.createCell(2).setCellValue("PickUp DATE/TIME");
			headerRow.createCell(3).setCellValue("DROP DATE/TIME");
			headerRow.createCell(4).setCellValue("JOB#");
			headerRow.createCell(5).setCellValue("PRIORITY");
			headerRow.createCell(6).setCellValue("P/U LOCATION");
			headerRow.createCell(7).setCellValue("DROP LOCATION");
			headerRow.createCell(8).setCellValue("NOTES");
			headerRow.createCell(9).setCellValue("UNIT#");
			headerRow.createCell(10).setCellValue("STATUS");
			headerRow.createCell(11).setCellValue("ASSIGNED TO");
			headerRow.createCell(12).setCellValue("CREATED BY");
			headerRow.createCell(13).setCellValue("LAST UPDATED");

			// Apply style to all header cells
			for (int i = 0; i < 14; i++) {
				headerRow.getCell(i).setCellStyle(cellStyle);
			}

			int rowNum = 1;
			for (TrailerLogExportDto trailerLogDto : trailerLogExportDtos) {
				Row row = sheet.createRow(rowNum++);
				row.createCell(0).setCellValue(trailerLogDto.getCreatedDate());
				String str = "";
				if(trailerLogDto.getJobNumber() != null) {
					str = "Move";
				}else {
					str = trailerLogDto.getActions();
				}
				row.createCell(1).setCellValue(str);
				row.createCell(2).setCellValue(trailerLogDto.getPickupDateTime());
				row.createCell(3).setCellValue(trailerLogDto.getDropDateTime());
				row.createCell(4).setCellValue(trailerLogDto.getJobNumber());
				row.createCell(5).setCellValue(trailerLogDto.getPriority() != null ? trailerLogDto.getPriority().toString() : "");
				
				String pickupLocation = trailerLogDto.getPickupLocationName();
				if (trailerLogDto.getPickupSpotName() != null) {
					pickupLocation += " " + trailerLogDto.getPickupSpotName();
				}
				row.createCell(6).setCellValue(pickupLocation);
				
				String dropLocation = trailerLogDto.getDropLocationName();
				if (trailerLogDto.getDropSpotName() != null) {
					dropLocation += " " + trailerLogDto.getDropSpotName();
				}
				row.createCell(7).setCellValue(dropLocation);
				
				row.createCell(8).setCellValue(trailerLogDto.getDescription());
				row.createCell(9).setCellValue(trailerLogDto.getFleetUnitNumber());
				row.createCell(10).setCellValue(trailerLogDto.getStatus() != null ? trailerLogDto.getStatus().toString() : "");
				row.createCell(11).setCellValue(trailerLogDto.getAssignedTo());
				row.createCell(12).setCellValue(trailerLogDto.getCreatedBy());
				row.createCell(13).setCellValue(trailerLogDto.getLastModifiedDate());
			}

			Path tmpDirPath = Files.createTempDirectory(BusinessConstants.EXPORT_TMP_DIRECTORY_PREFIX);
			File tmpFolder = tmpDirPath.toFile();
			String fileName = tmpFolder.getAbsolutePath() + File.separator + UUID.randomUUID().toString() + ".xlsx";
			try (OutputStream outputStream = new FileOutputStream(fileName)) {
				workbook.write(outputStream);
				return new InputStreamResource(new FileInputStream(fileName));
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.CLIENT_EXPORT, e.getMessage());
		}
	}
	
	@Override
	public Resource exportTrailerHistoryAsPDF(String fromDate, String toDate, Predicate predicate, String timeZone) {
		
		float[] columnWidths = {50f, 75f, 50f, 50f, 35f, 50f, 50f, 50f, 50f, 50f, 50f, 50f, 50f, 50f};
		PdfWriter writer;

		SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
	    SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
		String startDate = "";
		String endDate = "";
	    
	    try
	    {
	    	if(fromDate != null)
	    	{
	    		Date date1 = inputFormat.parse(fromDate);
	       	    startDate = outputFormat.format(date1);
	    	}
	    	 
	    	 if(toDate != null)
	    	 {
	    		Date date2 = inputFormat.parse(toDate);
	        	endDate = outputFormat.format(date2);
	    	 }	
	    }
	    catch (ParseException e) {
	        e.printStackTrace();
	    }
		
		
		if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
		      ZonedDateTime fromDateTime =
		          DateTimeUtils.convertStringToLocalDate(fromDate, BusinessConstants.FORM_DATE_FORMAT)
		              .atTime(LocalTime.MIN).atZone(ZoneId.of(timeZone));
		      ZonedDateTime toDateTime =
		          DateTimeUtils.convertStringToLocalDate(toDate, BusinessConstants.FORM_DATE_FORMAT)
		              .atTime(LocalTime.MAX).atZone(ZoneId.of(timeZone));
		      BooleanExpression betweenDateCriteria =
		          QTrailerLog.trailerLog.createdDate.between(fromDateTime, toDateTime);
		      predicate =
		          Objects.nonNull(predicate) ? betweenDateCriteria.and(predicate) : betweenDateCriteria;
		    }
		    

		    var logs = trailerLogRepository.findAll(predicate);
		    List<TrailerLogExportDto> logsExportDtos = new ArrayList<>();
		    logs.forEach(log -> logsExportDtos.add(trailerLogMapper.mapToExportDto(log, timeZone)));
		    
		    try {

				Document document = new Document();
				writer = PdfWriter.getInstance(document, new FileOutputStream("iTextTable.pdf"));
				document.open();
				PdfContentByte pdfContentByte = writer.getDirectContent();
				uploadJobPdfLogo(document, "classpath:trailer-audit-pdf-images/Picture3.png", 220f, 729f, 130f, 35f);	
				String latoFontPath = "/Font/Lato-Regular.ttf";
				java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
				if (fontStream == null) {
					throw new IOException("Font file not found");
				}
				BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
				com.itextpdf.text.Font blackFont = new com.itextpdf.text.Font(bf, 8, com.itextpdf.text.Font.NORMAL, BaseColor.BLACK);
				ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER, new Phrase("Trailer History"), 290, 720, 0);
				ColumnText.showTextAligned(pdfContentByte, Element.ALIGN_CENTER, new Phrase(startDate +" to "+ endDate, blackFont), 290, 700, 0);
				
				if (logsExportDtos.size() > 60) {
					
					List<TrailerLogExportDto> firstPageEntries = logsExportDtos.subList(0, 60);
					List<TrailerLogExportDto> remainingPageEntries = logsExportDtos.subList(61, logsExportDtos.size());
					
					PdfPTable table = modifyTrailerHistoryTableInPdf(columnWidths, firstPageEntries, true);
					table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());

					List<List<TrailerLogExportDto>> paginatedRows = Logspaginate(remainingPageEntries, 74);
					for (List<TrailerLogExportDto> pageRows : paginatedRows) {
						document.newPage();
						PdfPTable nextTable = modifyTrailerHistoryTableInPdf(columnWidths, pageRows, false);
						document.add(nextTable);

					}
				} else {
					
					PdfPTable table = modifyTrailerHistoryTableInPdf(columnWidths, logsExportDtos, true);
					table.writeSelectedRows(0, -1, 50, 660, writer.getDirectContent());
				}

				document.close();
				File pdfFile = new File("iTextTable.pdf");
				return new InputStreamResource(new FileInputStream(pdfFile));

			} catch (Exception e) {
				log.error("Error occurred while exporting Excel!", e);
				throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
			}
		    
	}

	private PdfPTable modifyTrailerHistoryTableInPdf(float[] columnWidths, List<TrailerLogExportDto> logsExportData, Boolean isFirstPage) {
		
		PdfPTable table = new PdfPTable(columnWidths);
		if (isFirstPage == true) {
			addTableHeaderInTrailerHistory(table);
		}
		addRowsInTrailerHistory(table, logsExportData);
		table.setTotalWidth(500);

		table.setLockedWidth(true);

		return table;
	}
	
	private void addRowsInTrailerHistory(PdfPTable table, List<TrailerLogExportDto> logsExportData) {
		try {
			long start = System.currentTimeMillis();
			
			for (TrailerLogExportDto logExportDto : logsExportData) {
				
				String latoFontPath = "/Font/Lato-Regular.ttf";
				java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
				if (fontStream == null) {
					throw new IOException("File not found");
				}
				LoggerUtil.logSLA(LOGGER, "################History Logs ######################"+fontStream, start, String.valueOf(logExportDto.getCreatedDate()));
				BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED,
						BaseFont.EMBEDDED, fontStream.readAllBytes(), null);
				com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 4, com.itextpdf.text.Font.NORMAL);

				
				
				addCellInPDFWithHeading(table, logExportDto.getCreatedDate(), font, BaseColor.WHITE, new BaseColor(221, 221, 223));
				String str = "";
				if(logExportDto.getJobNumber() != null) {
					str = "Move";
				}else {
					str = logExportDto.getActions();
				}
				addCellInPDFWithHeading(table, str, font, BaseColor.WHITE, new BaseColor(221, 221, 223));
				
				addCellInPDFWithHeading(table, logExportDto.getPickupDateTime(), font, BaseColor.WHITE, new BaseColor(221, 221, 223));
				
				addCellInPDFWithHeading(table, logExportDto.getDropDateTime(), font, BaseColor.WHITE, new BaseColor(221, 221, 223));
				
				addCellInPDFWithHeading(table, logExportDto.getJobNumber(), font, BaseColor.WHITE, new BaseColor(221, 221, 223));
				
				String priority = logExportDto.getPriority() != null ? logExportDto.getPriority().toString() : "";
				addCellInPDFWithHeading(table, priority , font, BaseColor.WHITE, new BaseColor(221, 221, 223));		
				if(logExportDto.getPickupSpotName() != null) {
					addCellInPDFWithHeading(table, logExportDto.getPickupLocationName().concat("  ").concat(logExportDto.getPickupSpotName()), font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}else {
					addCellInPDFWithHeading(table, "" , font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}
				
				if(logExportDto.getDropSpotName() != null) {
				
					addCellInPDFWithHeading(table, logExportDto.getDropLocationName().concat("  ").concat(logExportDto.getDropSpotName()), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				}
				else {
					
					addCellInPDFWithHeading(table, logExportDto.getDropLocationName(), font, BaseColor.WHITE,
							new BaseColor(221, 221, 223));
				}
				
				addCellInPDFWithHeading(table, logExportDto.getDescription(), font, BaseColor.WHITE, new BaseColor(221, 221, 223));
				
				addCellInPDFWithHeading(table, logExportDto.getFleetUnitNumber(), font, BaseColor.WHITE, new BaseColor(221, 221, 223));
				
				String status = logExportDto.getStatus() != null ? logExportDto.getStatus().toString() : "";
				addCellInPDFWithHeading(table, status, font, BaseColor.WHITE, new BaseColor(221, 221, 223));
				
				addCellInPDFWithHeading(table, logExportDto.getAssignedTo(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				
				addCellInPDFWithHeading(table, logExportDto.getCreatedBy(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));
				
				addCellInPDFWithHeading(table, logExportDto.getLastModifiedDate(), font, BaseColor.WHITE,
						new BaseColor(221, 221, 223));

			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
	}
	
	private void addTableHeaderInTrailerHistory(PdfPTable table) {
		try {
			String latoFontPath = "/Font/Lato-SemiBold.ttf";
			java.io.InputStream fontStream = TrailerAuditServiceImpl.class.getResourceAsStream(latoFontPath);
			if (fontStream == null) {
				throw new IOException("File not found");
			}
			BaseFont bf = BaseFont.createFont(latoFontPath, BaseFont.CP1252, BaseFont.NOT_EMBEDDED, BaseFont.EMBEDDED,
					fontStream.readAllBytes(), null);
			com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, 6, com.itextpdf.text.Font.NORMAL,
					BaseColor.BLACK);
			Stream.of("Creation Date", "Action", "Pick Up DateTime", "Drop DateTime", "Job#", "Priority", "Pick Up Location", "Drop Location", "Notes", "Trailer #", "Status", "Assigned To", "Created By", "Last Updated")
			
					.forEach(columnTitle -> {
						PdfPCell header = new PdfPCell();
						header.setBorderColor(new BaseColor(221, 221, 223));
						header.setPhrase(new Phrase(columnTitle, font));
						header.setHorizontalAlignment(Element.ALIGN_LEFT);
						header.setVerticalAlignment(Element.ALIGN_MIDDLE);
						header.setFixedHeight(20f);
						table.addCell(header);
					});

			for (int i = 0; i < 14; i++) {
				PdfPCell borderCell = new PdfPCell();
				borderCell.setBackgroundColor(new BaseColor(205, 205, 205));
				borderCell.setBorderColor(new BaseColor(221, 221, 223));
				borderCell.setFixedHeight(5);
				table.addCell(borderCell);
			}
		} catch (Exception e) {
			log.error("Error occurred while exporting Excel!", e);
			throw new ServiceException(ErrorCode.MOVES_EXPORT_PDF, e.getMessage());
		}
	}
	
	private List<List<TrailerLogExportDto>> Logspaginate(List<TrailerLogExportDto> rows, int rowsPerPage) {

		return Stream.iterate(0, i -> i + rowsPerPage).limit((long) Math.ceil((double) rows.size() / rowsPerPage))
				.map(i -> rows.subList(i, Math.min(i + rowsPerPage, rows.size()))).collect(Collectors.toList());
	}
}
