package com.ma.spoton.api.services;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.entities.ContactUs;
import com.ma.spoton.api.entities.GuardEntryExit;
import com.ma.spoton.api.entities.GuardEntryExit.Type;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.Message;
import com.ma.spoton.api.entities.Notification;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.mappers.NotificationMapper;
import com.ma.spoton.api.repositories.NotificationRepository;
import com.ma.spoton.api.requests.MessageRequest;
import com.ma.spoton.api.requests.NotificationRequest;
import com.ma.spoton.api.utils.JwtTokenUtils;
import com.ma.spoton.api.utils.LoggerUtil;
import com.ma.spoton.api.utils.DateTimeUtils;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.EmailMessageRequest;
import com.ma.spoton.api.dtos.EmailResponse;
import com.ma.spoton.api.dtos.MailRecipient;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationServiceImpl implements NotificationService {

  private final NotificationRepository notificationRepository;
  private final SendNotificationService sendNotificationService;
  private static final Logger LOGGER = LoggerFactory.getLogger(NotificationServiceImpl.class);
  
  @Autowired
  private NotificationMapper notificationMapper;
  
  @Autowired
  private JwtTokenUtils jwtTokenUtils;
  
  private final EmailService emailService;
  
  @Value("${application.admin-portal-base-url}")
  private String adminPortalBaseUrl;
  
  @Override
  @Transactional
  public void autoSendNotification() {
    // log.info(">> autoSendNotification()");
    List<Notification> notificationList =
        notificationRepository.findAllByDeliveryStatus(Notification.DeliveryStatus.PENDING);
    notificationList.forEach(notification -> sendNotificationService.send(notification.getUuid()));
  }

  @Override
  public void createAndSendForgotPasswordNotification(User user) {
//    log.info(">> sendForgotPasswordNotification({})", user);

    // For email notification
    var emailTitle = "Forgot password token email.";
    String emailBody = "<h2> Hi " + user.getFirstName() + " " + user.getLastName() + ", </h2>"
        + "<h1>" + user.getForgotPasswordToken() + "</h1>"
        + "<h3> This is your generated token for spoton forgot password request, you can reset your password with the help of this token.";
    var notificationEmail = createEmailNotification(emailTitle, emailBody, null, null, user);

    // Immediately Sending notification
    sendNotificationService.send(notificationEmail.getUuid());
  }

  @Override
  public void createHotTrailerAlert(User fromUser, User toUser, String fleetUnitNumber,
      String clientName) {
//    log.info(">> sendHotTrailerAlert({}, {}, {}, {})", fromUser, toUser, fleetUnitNumber,
//        clientName);

    // For email notification
    var emailTitle = "Hot Trailer Alert";
    String emailBody = "<h2> Hi " + toUser.getFirstName() + " " + toUser.getLastName() + ", </h2>"
        + "<h3>Hot Trailer (" + fleetUnitNumber + ") has arrived in \"" + clientName + "\".</h3>";
    createEmailNotification(emailTitle, emailBody, null, fromUser, toUser);
  }

  @Override
  public void createNewUserNotification(User user) {
 //   log.info(">> sendNewUserNotification");
    String userName = user.getFirstName() + " " + user.getLastName();
    String token = jwtTokenUtils.generateToken(userName, user.getUuid());
//    log.info("token>>>{}",token);
    // For email notification
    var emailTitle = "Spot On Account Created";
    String emailBody = "<h1> Hi " + user.getFirstName() + " " + user.getLastName() + ", </h1>"
        + "<h2>Click the below button to set password and activate your Spot On account</h2>"
        + "<p><a href=\"" + adminPortalBaseUrl + "/#/new-user?token=" + token 
    		+ "\" style=\"background-color:#4CAF50;color:white;padding:12px 20px;text-align:center;text-decoration:none;display:inline-block;font-size:16px;margin-top:20px;\">Click here to login</a></p>";
    createEmailNotification(emailTitle, emailBody, null, null, user);
  }

  @Override
  public void createNewJobNotification(Job job) {
//    log.info(">> createJobCreatedNotification({})", job);

    // Creating push notification
    var messageTitle = String.format("New Spot (SP%d) Assigned", job.getId());
    var messageBody =
        String.format("Spot (SP%d) assigned to move trailer/container (%s) from Location (%s - %s)",
            job.getId(), job.getFleet().getUnitNumber(), job.getPickupLocation().getLocationName(),
            job.getPickupSpot().getSpotName());
    createPushNotification(messageTitle, messageBody, job.getUuid(), null, job.getAssignedTo());
  }

  @Override
  public void createUpdateJobNotification(Job job) {
//    log.info(">> createUpdateJobNotification({})", job);

    // Creating push notification
    var messageTitle = String.format("Spot (SP%d) Changed", job.getId());
    var messageBody =
        String.format("Spot (SP%d) changed to move trailer/container (%s) from Location (%s - %s)",
            job.getId(), job.getFleet().getUnitNumber(), job.getPickupLocation().getLocationName(),
            job.getPickupSpot().getSpotName());
    createPushNotification(messageTitle, messageBody, job.getUuid(), null, job.getAssignedTo());
  }

  @Override
  public void createFleetEntryExitNotification(GuardEntryExit guardEntryExit, User user) {
//    log.info(">> createFleetEntryExitNotification({}, {})", guardEntryExit, user);

    // Creating push notification
    String action = Type.ENTRY.equals(guardEntryExit.getType()) ? "Entered" : "Exited";
    String actionWithVerb =
        Type.ENTRY.equals(guardEntryExit.getType()) ? "Entered at" : "Exited from";
    var messageTitle =
        String.format("Trailer (%s) has %s", guardEntryExit.getFleet().getUnitNumber(), action);
    var messageBody = String.format("Trailer (%s) has %s Location (%s)",
        guardEntryExit.getFleet().getUnitNumber(), actionWithVerb,
        guardEntryExit.getLocation().getLocationName());
    createPushNotification(messageTitle, messageBody, guardEntryExit.getUuid(), null, user);
  }

  private Notification createEmailNotification(String messageTitle, String messageBody,
      String messageReferenceId, User fromUser, User toUser) {
    var notificationRequest =
        NotificationRequest.builder().channel(Notification.Channel.EMAIL).messageTitle(messageTitle)
            .messageBody(messageBody).messageReferrenceId(messageReferenceId).startTime(null)
            .deliveryStatus(Notification.DeliveryStatus.PENDING).failureReason(null).build();
    return createNotification(notificationRequest, fromUser, toUser);
  }
  

  private Notification createPushNotification(String messageTitle, String messageBody,
      String messageReferenceId, User fromUser, User toUser) {
    var notificationRequest =
        NotificationRequest.builder().channel(Notification.Channel.PUSH).messageTitle(messageTitle)
            .messageBody(messageBody).messageReferrenceId(messageReferenceId).startTime(null)
            .deliveryStatus(Notification.DeliveryStatus.PENDING).failureReason(null).build();
    return createNotification(notificationRequest, fromUser, toUser);
  }

  private Notification createNotification(NotificationRequest notificationRequest, User fromUser,
      User toUser) {
    var notification = notificationMapper.mapToEntity(notificationRequest, null);
    notification.setFromUser(fromUser);
    notification.setToUser(toUser);
    return notificationRepository.save(notification);
  }
  

@Override
public void createAndSendDeleteAccountAlertNotification(User user) {
	// TODO Auto-generated method stub
//	log.info("createAndSendDeleteAccountAlertNotification");

    // For email notification
    var emailTitle = "Inactive Account Deactivation.";
    String emailBody = "<p> Hi " + user.getFirstName() + " " + user.getLastName() + ", </p>"
        + "<p> Due to inactivity, your Ablair Spot On account will be deactivated in <b>5 days</b>. Please login to ensure that your account is not deactivated. </p>";
    var notificationEmail = createEmailNotification(emailTitle, emailBody, null, null, user);

    // Immediately Sending notification
    sendNotificationService.send(notificationEmail.getUuid());
}

@Override
public void createNewMessageNotification(Message message) {
	// TODO Auto-generated method stub
	 
	    String emailBody="";
	    var emailTitle = "You have received a new message in Spot On";
	    if(message.getType().toString() == "TRAILER_TRUCK_MOVE")
	    {
	    	emailBody+="<h3>Trailer Truck Move</h3>"
	    			+"<p>Pickup Location : "+ message.getPickupLocation().getLocationName() +"</p>"
	    			+"<p>Pickup Parking Spot : "+message.getPickupSpot().getSpotName()+"</p>"
	    			+"<p>Drop Location : "+message.getDropLocation().getLocationName()+"</p>"
	    			+"<p>Drop Parking Spot : "+message.getDropSpot().getSpotName()+"</p>";
	    }
	    else if(message.getType().toString() == "HOT_TRAILER")
	    {
	    	emailBody+="<h3>Hot Trailer</h3>"
	    	+"<p>Client Location :"+ message.getClientLocation().getLocationName() +"</p>"
	    	+"<p>Hot Trailer# :"+message.getFleet().getUnitNumber()+"</p>";
	    }
	    else
	    {
	    	emailBody+="<h3>Information</h3>";
	    }
	    emailBody += "<p>Message : "+message.getMessageBody()+"</p>"
	    		+"<p> &nbsp; </p>"
	        +"<p>"+message.getCreatedBy().getFirstName()+" "+message.getCreatedBy().getLastName()+"</p>";
	    createEmailNotification(emailTitle, emailBody, null, null, message.getToUser());
}

@Override
public void createJobFromMessageNotification(Job job, User user, String timeZone) {
//  log.info(">> createJobFromMessageNotification({})", job);

  var date = DateTimeUtils.convertZonedDateTimeToString(job.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone);
  
  var messageTitle = String.format("Spot Request Sent! "+ date);
  var messageBody="<h3>Spot Request Sent! "+ date + "</h3>"
  		+ "<p>Spot Number : SP"+ job.getId() +"</p>"
			+"<p>Trailer Number : "+job.getFleet().getUnitNumber()+"</p>";
  if(job.getDescription() != null && job.getDescription() != "") {
	  messageBody += "<p>Notes : "+job.getDescription()+"</p>";
  }
  messageBody += "<p>Thanks again!</p>";
  createEmailNotification(messageTitle, messageBody, null, null, user);
}

@Override
public void createContactUsEmailNotification(ContactUs contact) {
	
		long start = System.currentTimeMillis(); 
	    var emailBody = "<p>From: " + contact.getFromUser().getFirstName() + " " + contact.getFromUser().getLastName() + " - " 
	    				+ contact.getFromUser().getEmail() + "</p>"
	    				+ "<p>Subject: "+contact.getType() + " - "+ contact.getSubject()+"</p>"
	    				+ "<p><div>Message:</div>" + contact.getMessageBody() + "</p>";
	    var emailTitle = "New message from the Spot On feedback/support form!";
	    
	    var emailMessageRequest = EmailMessageRequest.builder().isAttachment(false).attachmentMap(null)
	            .bccRecipients(null).ccRecipients(null)
//	            .recipients(List.of(new MailRecipient(null, "<EMAIL>")))
	            .recipients(List.of(new MailRecipient(null, "<EMAIL>")))
	            .content(emailBody).replyTo(contact.getFromUser().getEmail())
	            .subject(emailTitle).build();
	    EmailResponse res = emailService.sendEmail(emailMessageRequest);
	    
	    if (res != null && res.getStatusCode() == 202) {
	    	
	        var replyEmailBody = "<p><div>Hi there,</div>"+
	        		"Thank you for reaching out to us! We have received your message and will get back to you shortly.</p><p>&nbsp;</p>"+"<p>Here is a summary of your submission:</p>"+
	        		"<p>Subject: " + contact.getType() + " - " + contact.getSubject() + "<p><div>Message:</div>"+
	        		contact.getMessageBody() + "</p><p>&nbsp;</p>"+"<p>We appreciate your feedback!</p><p>&nbsp;</p>"+"<p><div>Regards,</div>"+"The Spot On Team</p>";

	        var replyEmail = EmailMessageRequest.builder()
	                .isAttachment(false)
	                .attachmentMap(null)
	                .bccRecipients(null)
	                .ccRecipients(null)
	                .recipients(List.of(new MailRecipient(null, contact.getFromUser().getEmail())))
	                .content(replyEmailBody).replyTo("<EMAIL>")
//	                .content(replyEmailBody).replyTo("<EMAIL>")
	                .subject("Spot On - Thank you for your feedback!")
	                .build();

	        emailService.sendEmail(replyEmail);
	    }
}

}
