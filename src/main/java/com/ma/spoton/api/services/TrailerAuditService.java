package com.ma.spoton.api.services;

import java.util.List;

import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;

import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.TrailerAuditDto;
import com.ma.spoton.api.entities.Job.FleetStatus;
import com.ma.spoton.api.entities.TrailerAudit;
import com.ma.spoton.api.requests.TrailerAuditRequest;
import com.querydsl.core.types.Predicate;

public interface TrailerAuditService {
	
	void createTrailerAudit(TrailerAuditRequest[] trailerAuditRequests);
	
	void clearSpot(String spotId);
	
	Resource exportTrailerAuditAsEXCEL(String clientId, String locationIds, FleetStatus fleetStatus , Predicate predicate, String timeZone);
	
	Resource exportTrailerAuditAsPdf(String clientId, String locationIds, FleetStatus fleetStatus , Predicate predicate, String timeZone);
	
}
