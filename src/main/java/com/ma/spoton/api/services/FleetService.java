package com.ma.spoton.api.services;

import java.util.List;

import com.ma.spoton.api.dtos.*;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.requests.ClientFleetRequest;
import com.ma.spoton.api.requests.FleetRequest;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.Page;

public interface FleetService {

	FleetDto createFleet(FleetRequest fleetRequest, List<String> clientIds);

	void updateFleet(String fleetId, FleetRequest fleetRequest);

	void deleteFleet(String fleetId);

	FleetDto getFleet(String fleetId, String timeZone, List<String> clientIds);

	PagedResponse<FleetDto> getFleets(Predicate predicate, Pageable pageable, String timeZone,
									  List<String> clientIds,String locationIds, String unit_sequenceNumber);
	
	Page<String> getUniqueCarrierPage(String carrier, Pageable pageable);

	void assignFleets(String clientId, ClientFleetRequest assignFleetRequest);

	void assignFleetsAfterMerging(FleetMergeDto fleetMergeDto);

	void unassignFleets(String clientId, ClientFleetRequest clientFleetRequest);

	PagedResponse<GuardEntryExitDto> getFleetEntryExits(Predicate predicate, Pageable pageable,
														String timeZone, List<String> clientIds, String fromDate, String toDate);

	Resource exportFleetsAsCSV(Predicate predicate, String timeZone, List<String> clientIds);

	Resource exportFleetsAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds,String locationIds);

	Resource exportFleetEntryExitsAsCSV(Predicate predicate, String timeZone, List<String> clientIds);

	Resource exportFleetEntryExitsAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds, String fromDate, String toDate);
	
	Resource exportEntryExitsReportAsEXCEL(Predicate predicate, String timeZone, List<String> clientIds, String fromDate, String toDate);
	
	Resource exportEntryExitsReportAsPDF(Predicate predicate, String timeZone, List<String> clientIds, String fromDate, String toDate);

	void deactivateFleet(String fleetId);

	void activateFleet(String fleetId);

	boolean mergeFleet(FleetMergeDto fleetMergeDto);

	List<String> getUniqueCarrier(String carrier);

	List<String> getUniqueCarrier();

	int updateCarrierInFleet(FleetCarrierUpdate fleetCarrierUpdate);

	Resource exportFleetsAsPDF(Predicate predicate, String timeZone, List<String> clientIds,String locationIds);
	
	PagedResponse<TrailerLogDto> getTrailerLogs(Predicate predicate, Pageable pageable, String timeZone,
			  List<String> clientIds, String fromDate, String toDate);
	
	Resource exportTrailerHistorysAsEXCEL(String fromDate, String toDate, Predicate predicate, String timeZone);
    
    Resource exportTrailerHistoryAsPDF(String fromDate, String toDate, Predicate predicate, String timeZone);
}
