package com.ma.spoton.api.services;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.UserAvailabilityExceptionDto;
import com.ma.spoton.api.entities.OverTime;
import com.ma.spoton.api.entities.QUserAvailabilityException;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.entities.UserAvailabilityException;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.mappers.UserAvailabilityExceptionMapper;
import com.ma.spoton.api.repositories.OverTimeRepository;
import com.ma.spoton.api.repositories.UserAvailabilityExceptionRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.requests.UserAvailbilityExceptionRequest;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;

@Service
public class UserAvailabilityExceptionServiceImpl implements UserAvailabilityExceptionService{

	@Autowired
	private UserAvailabilityExceptionMapper userAvailabilityExceptionMapper;
	
	@Autowired
	private UserAvailabilityExceptionRepository userAvailabilityExceptionRepository;
	
	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private OverTimeRepository overTimeRepository;
	
	@Override
	@Transactional
	public UserAvailabilityException addUserAvailabilityException(UserAvailbilityExceptionRequest userAvailabilityExceptionRequest) {
		
//		UserAvailabilityException userAvailabilityException = userAvailabilityExceptionMapper.mapToEntity(userAvailabilityExceptionRequest);
//		User user = userRepository.findByUuid(userAvailabilityExceptionRequest.getUserId())
//				.orElseThrow(() -> new com.ma.spoton.api.exception.ServiceException(ErrorCode.USER_NOT_FOUND, userAvailabilityExceptionRequest.getUserId()));	
//		userAvailabilityException.setUser(user);
//		return userAvailabilityExceptionRepository.save(userAvailabilityException);
	
		UserAvailabilityException userAvailabilityException = null;
		UserAvailabilityException userAvailabilityException1 = null;
		UserAvailabilityException userAvailabilityException2 = null;
		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);
		String userAvailabilityExceptionIdA = null;
		String userAvailabiltiyExceptionIdB = null;
		String userAvailabilityExceptionId1 = null;
		String userAvailabilityExceptionId2 = null;
		String unwanteduserAvailabilityExceptionId = null;
		Boolean isPreviousDayRemainingJob = false;

		// Checking whether the UserAvailabilityRequest starts and ends on the same day
		if (userAvailabilityExceptionRequest.getStartingTime().isBefore(userAvailabilityExceptionRequest.getEndingTime())) {
			
			// Fetching all UserAvailabilities of that particular user in the same day
			List<UserAvailabilityException> userAvailabilityExceptions = userAvailabilityExceptionRepository
					.findAllByUserDateAndType(userAvailabilityExceptionRequest.getUserId(), userAvailabilityExceptionRequest.getDate(), userAvailabilityExceptionRequest.getType());
			for (UserAvailabilityException userAvailabilityExceptionA : userAvailabilityExceptions) {
				// Checking whether a UserAvailability exists in that particular day and it
				// should not be the remaining work of previous day
				if (userAvailabilityExceptionA.getIsPreviousDayRemainingWork().equals(Boolean.FALSE)) {
					userAvailabilityExceptionIdA = userAvailabilityExceptionA.getUuid();
					break;
				}
			}
//			if (userAvailabilityExceptionIdA == null) {
//				// Checking whether a UserAvailability exists in that particular day and it
//				// should not be the remaining work of previous day{case 2}
//				for (UserAvailabilityException userAvailabilityExceptionA : userAvailabilityExceptions) {
//					// Checking if userAvailabilityA starts on 00:00am
//					if (userAvailabilityExceptionA.getStartingTime().equals(dayStartTime)) {
//						// Getting the list of previous day user availabilities
//						List<UserAvailabilityException> previousDayUserAvailabilityExceptions = userAvailabilityExceptionRepository
//								.findAllByUserDateAndType(userAvailabilityExceptionRequest.getUserId(),
//										userAvailabilityExceptionRequest.getDate().minusDays(1), userAvailabilityExceptionRequest.getType());
//						for (UserAvailabilityException userAvailabilityExceptionB : previousDayUserAvailabilityExceptions) {
//							// Checking if userAvailabiltyA is the remaining work of userAvailabilityB done
//							// on next day
//							if (userAvailabilityExceptionB.getEndingTime().equals(dayEndTime)) {
//								isPreviousDayRemainingJob = true;
//							}
//
//						}
//						if (isPreviousDayRemainingJob == false) {
//							userAvailabilityExceptionIdA = userAvailabilityExceptionA.getUuid();
//							break;
//						}
//
//					}
//				}
//			}

			if (userAvailabilityExceptionIdA != null) {
				userAvailabilityException = userAvailabilityExceptionRepository.findByAvailabilityExceptionId(userAvailabilityExceptionIdA);
			}

			// if there is no UserAvailability present
			if (userAvailabilityException == null) {
				userAvailabilityException = userAvailabilityExceptionMapper.mapToEntity(userAvailabilityExceptionRequest);
				User user = userRepository.findByUuid(userAvailabilityExceptionRequest.getUserId()).orElseThrow(
						() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userAvailabilityExceptionRequest.getUserId()));
				userAvailabilityException.setUser(user);
//				userAvailabilityException.setIsActive(userAvailabilityExceptionRequest.getActive());
			} else {

				// Checking whether the user availability ending time is 23:59pm
				if (userAvailabilityException.getEndingTime().equals(dayEndTime)) {
					// Fetching all the next day user availabilities
					List<UserAvailabilityException> nextDayUserAvailabilityExceptions = userAvailabilityExceptionRepository.findAllByUserDateAndType(
							userAvailabilityExceptionRequest.getUserId(), userAvailabilityExceptionRequest.getDate().plusDays(1), userAvailabilityExceptionRequest.getType());
					for (UserAvailabilityException userAvailabilityExceptionB : nextDayUserAvailabilityExceptions) {
						// Checking whether it is a continuation of the previous day availability
						if (userAvailabilityExceptionB.getStartingTime().equals(dayStartTime) && userAvailabilityExceptionB.getIsPreviousDayRemainingWork().equals(Boolean.TRUE)) {
							unwanteduserAvailabilityExceptionId = userAvailabilityExceptionB.getUuid();
						}
					}

					// Deleting unwanted UserAvailability
					if (unwanteduserAvailabilityExceptionId != null) {
						UserAvailabilityException unWantedAvailability = userAvailabilityExceptionRepository
								.findByAvailabilityExceptionId(unwanteduserAvailabilityExceptionId);
						userAvailabilityExceptionRepository.delete(unWantedAvailability);

					}
				}

				userAvailabilityException.setDate(userAvailabilityExceptionRequest.getDate());
				userAvailabilityException.setStartingTime(userAvailabilityExceptionRequest.getStartingTime());
				userAvailabilityException.setEndingTime(userAvailabilityExceptionRequest.getEndingTime());
//				userAvailabilityException.setIsActive(userAvailabilityExceptionRequest.getActive());
				if (userAvailabilityExceptionRequest.getBreakStartingTime() != null
						&& userAvailabilityExceptionRequest.getBreakEndingTime() != null) {
					userAvailabilityException.setBreakStartingTime(userAvailabilityExceptionRequest.getBreakStartingTime());
					userAvailabilityException.setBreakEndingTime(userAvailabilityExceptionRequest.getBreakEndingTime());
				}
				else if (userAvailabilityExceptionRequest.getBreakStartingTime() == null
						&& userAvailabilityExceptionRequest.getBreakEndingTime() == null) {
					userAvailabilityException.setBreakStartingTime(null);
					userAvailabilityException.setBreakEndingTime(null);
				}
				

			}

			userAvailabilityException.setIsPreviousDayRemainingWork(Boolean.FALSE);
			return userAvailabilityExceptionRepository.save(userAvailabilityException);

		}

		// For user Availability Request which starts on a day and ends next day
		else {

			List<UserAvailabilityException> userAvailabilityExceptions1 = userAvailabilityExceptionRepository
					.findAllByUserDateAndType(userAvailabilityExceptionRequest.getUserId(), userAvailabilityExceptionRequest.getDate(), userAvailabilityExceptionRequest.getType());
			List<UserAvailabilityException> userAvailabilityExceptions2 = userAvailabilityExceptionRepository.findAllByUserDateAndType(
					userAvailabilityExceptionRequest.getUserId(), userAvailabilityExceptionRequest.getDate().plusDays(1), userAvailabilityExceptionRequest.getType());

			if (userAvailabilityExceptions1.size() > 0) {
				for (UserAvailabilityException userAvailabilityExceptionA : userAvailabilityExceptions1) {
					if (userAvailabilityExceptionA.getEndingTime().equals(dayEndTime) && userAvailabilityExceptionA.getIsPreviousDayRemainingWork().equals(Boolean.FALSE)) {
						userAvailabilityExceptionId1 = userAvailabilityExceptionA.getUuid();
					}
				}
			}

			if (userAvailabilityExceptions2.size() > 0) {
				for (UserAvailabilityException userAvailabilityExceptionB : userAvailabilityExceptions2) {
					if (userAvailabilityExceptionB.getStartingTime().equals(dayStartTime) && userAvailabilityExceptionB.getIsPreviousDayRemainingWork().equals(Boolean.TRUE)) {
						userAvailabilityExceptionId2 = userAvailabilityExceptionB.getUuid();
					}
				}
			}

			if (userAvailabilityExceptionId1 != null) {
				userAvailabilityException1 = userAvailabilityExceptionRepository.findByAvailabilityExceptionId(userAvailabilityExceptionId1);
			}

			if (userAvailabilityExceptionId2 != null) {
				userAvailabilityException2 = userAvailabilityExceptionRepository.findByAvailabilityExceptionId(userAvailabilityExceptionId2);
			}

			// If User Availabilities already in DB starts in one day and ends next day
			if (userAvailabilityException1 != null && userAvailabilityException2 != null) {

				userAvailabilityException1.setStartingTime(userAvailabilityExceptionRequest.getStartingTime());
//				userAvailabilityException1.setIsActive(userAvailabilityExceptionRequest.getActive());

				userAvailabilityException2.setEndingTime(userAvailabilityExceptionRequest.getEndingTime());
//				userAvailability2.setIsActive(userAvailabilityRequest.getActive());
				
				
				if(userAvailabilityExceptionRequest.getBreakStartingTime() != null && userAvailabilityExceptionRequest.getBreakEndingTime() != null)
				{
					//Break time is within same day
					if(userAvailabilityExceptionRequest.getBreakStartingTime().isBefore(userAvailabilityExceptionRequest.getBreakEndingTime()))
					{
						//Break time is in second day
						if(userAvailabilityExceptionRequest.getBreakStartingTime().isBefore(noon) && userAvailabilityExceptionRequest.getBreakEndingTime().isBefore(noon))
						{
							userAvailabilityException2.setBreakStartingTime(userAvailabilityExceptionRequest.getBreakStartingTime());
							userAvailabilityException2.setBreakEndingTime(userAvailabilityExceptionRequest.getBreakEndingTime());
							
							userAvailabilityException1.setBreakStartingTime(null);
							userAvailabilityException1.setBreakEndingTime(null);
						}
						
						//Break time is in first day
						if(userAvailabilityExceptionRequest.getBreakStartingTime().isAfter(noon) && userAvailabilityExceptionRequest.getBreakEndingTime().isAfter(noon))
						{
							userAvailabilityException1.setBreakStartingTime(userAvailabilityExceptionRequest.getBreakStartingTime());
							userAvailabilityException1.setBreakEndingTime(userAvailabilityExceptionRequest.getBreakEndingTime());
							
							userAvailabilityException2.setBreakStartingTime(null);
							userAvailabilityException2.setBreakEndingTime(null);
						}			
					}
					//Break time is split b/w two days
					else
					{
						
						userAvailabilityException1.setBreakStartingTime(userAvailabilityExceptionRequest.getBreakStartingTime());
						userAvailabilityException1.setBreakEndingTime(dayEndTime);
						
						userAvailabilityException2.setBreakStartingTime(dayStartTime);
						userAvailabilityException2.setBreakEndingTime(userAvailabilityExceptionRequest.getBreakEndingTime());			
					}
				}
				
			}
			// If User Availability already in DB starts and ends on same day or useravailability not in database
			else {

				userAvailabilityExceptionId1 = null;
				if (userAvailabilityExceptions1.size() > 0) {
					for (UserAvailabilityException userAvailabilityExceptionA : userAvailabilityExceptions1) {
						
						//user availability not continuation of previous day
						if(userAvailabilityExceptionA.getIsPreviousDayRemainingWork().equals(Boolean.FALSE))
						{
							userAvailabilityExceptionId1 = userAvailabilityExceptionA.getUuid();
							break;
						}
					}
					
//				if(userAvailabilityExceptionId1 == null)
//				{
//					
//					for (UserAvailabilityException userAvailabilityExceptionA : userAvailabilityExceptions1) {
//						
//						//user availability not continuation of previous day{case 2}
//						if(userAvailabilityExceptionA.getStartingTime().equals(dayStartTime))
//						{
//							
//							List<UserAvailabilityException> previousDayUseravailabilityExceptions = userAvailabilityExceptionRepository.findAllByUserDateAndType(userAvailabilityExceptionA.getUser().getUuid(), userAvailabilityExceptionA.getDate().minusDays(1), userAvailabilityExceptionA.getType());
//							for(UserAvailabilityException userAvailabilityExceptionB : previousDayUseravailabilityExceptions)
//							{
//								if(userAvailabilityExceptionB.getEndingTime().equals(dayEndTime))
//								{
//									isPreviousDayRemainingJob = true;
//									break;
//								}
//							}
//							if(isPreviousDayRemainingJob == false)
//							{
//								userAvailabilityExceptionId1 = userAvailabilityExceptionA.getUuid();
//							}
//						}
//					}				
//				}
			}
				if (userAvailabilityExceptionId1 != null) {
					userAvailabilityException1 = userAvailabilityExceptionRepository.findByAvailabilityExceptionId(userAvailabilityExceptionId1);
				}
				// If User Availability already in DB starts and ends on same day
				if (userAvailabilityException1 != null) {
					userAvailabilityException1.setStartingTime(userAvailabilityExceptionRequest.getStartingTime());
					userAvailabilityException1.setEndingTime(dayEndTime);
				}
				// If User Availability doesnot exist
				else if (userAvailabilityException1 == null) {
					userAvailabilityException1 = userAvailabilityExceptionMapper.mapToEntity(userAvailabilityExceptionRequest);
					User user = userRepository.findByUuid(userAvailabilityExceptionRequest.getUserId()).orElseThrow(
							() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userAvailabilityExceptionRequest.getUserId()));
					userAvailabilityException1.setUser(user);
					userAvailabilityException1.setEndingTime(dayEndTime);
//					userAvailabilityException1.setIsActive(userAvailabilityExceptionRequest.getActive());
				}

				userAvailabilityException2 = userAvailabilityExceptionMapper.mapToEntity(userAvailabilityExceptionRequest);
				User user = userRepository.findByUuid(userAvailabilityExceptionRequest.getUserId()).orElseThrow(
						() -> new ServiceException(ErrorCode.USER_NOT_FOUND, userAvailabilityExceptionRequest.getUserId()));
				userAvailabilityException2.setUser(user);
				userAvailabilityException2.setDate(userAvailabilityExceptionRequest.getDate().plusDays(1));
				userAvailabilityException2.setStartingTime(dayStartTime);
				userAvailabilityException2.setEndingTime(userAvailabilityExceptionRequest.getEndingTime());
//				userAvailabilityException2.setIsActive(userAvailabilityRequest.getActive());

				if (userAvailabilityExceptionRequest.getBreakStartingTime() != null
						&& userAvailabilityExceptionRequest.getBreakEndingTime() != null) {
					//Break time on same day
					if (userAvailabilityExceptionRequest.getBreakStartingTime()
							.isBefore(userAvailabilityExceptionRequest.getBreakEndingTime())) {
						//break time on day1
						if (userAvailabilityExceptionRequest.getBreakStartingTime().isAfter(noon)
								&& userAvailabilityExceptionRequest.getBreakEndingTime().isAfter(noon)) {
							userAvailabilityException1.setBreakStartingTime(userAvailabilityExceptionRequest.getBreakStartingTime());
							userAvailabilityException1.setBreakEndingTime(userAvailabilityExceptionRequest.getBreakEndingTime());
							
							userAvailabilityException2.setBreakStartingTime(null);
                            userAvailabilityException2.setBreakEndingTime(null); 
						}
						//break time on day2
						else if (userAvailabilityExceptionRequest.getBreakStartingTime().isBefore(noon)
								&& userAvailabilityExceptionRequest.getBreakEndingTime().isBefore(noon)) {
							userAvailabilityException2.setBreakStartingTime(userAvailabilityExceptionRequest.getBreakStartingTime());
							userAvailabilityException2.setBreakEndingTime(userAvailabilityExceptionRequest.getBreakEndingTime());
							
							userAvailabilityException1.setBreakStartingTime(null);
							userAvailabilityException1.setBreakEndingTime(null);
						}
					} 
					//break time b/w 2 days
					else if (userAvailabilityExceptionRequest.getBreakStartingTime()
							.isAfter(userAvailabilityExceptionRequest.getBreakEndingTime())) {
						userAvailabilityException1.setBreakStartingTime(userAvailabilityExceptionRequest.getBreakStartingTime());
						userAvailabilityException1.setBreakEndingTime(dayEndTime);

						userAvailabilityException2.setBreakStartingTime(dayStartTime);
						userAvailabilityException2.setBreakEndingTime(userAvailabilityExceptionRequest.getBreakEndingTime());
					}
				}
			}
			userAvailabilityException1.setIsPreviousDayRemainingWork(Boolean.FALSE);
			userAvailabilityException2.setIsPreviousDayRemainingWork(Boolean.TRUE);
			userAvailabilityExceptionRepository.save(userAvailabilityException2);
			return userAvailabilityExceptionRepository.save(userAvailabilityException1);

		}	
	}

	@Override
	@Transactional
	public List<UserAvailabilityExceptionDto> getUserAvailabilityException(String userId) {
	
		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);

		// For 3rd shift users
		List<UserAvailabilityException> thirdShiftUserAvailabilityExceptions = new ArrayList<>();
		List<UserAvailabilityException> mergedUserAvailabilityExceptions = new ArrayList<>();
		List<UserAvailabilityException> unWantedUserAvailabilityExceptions = new ArrayList<>();
	

		List<UserAvailabilityException> userAvailabilityExceptions = userAvailabilityExceptionRepository.findByUserId(userId);
		for (UserAvailabilityException userAvailabilityException : userAvailabilityExceptions) {
			if (userAvailabilityException.getStartingTime().equals(dayStartTime) && userAvailabilityException.getIsPreviousDayRemainingWork().equals(Boolean.TRUE)
					|| userAvailabilityException.getEndingTime().equals(dayEndTime)) {
				thirdShiftUserAvailabilityExceptions.add(userAvailabilityException);

			}
		}
		userAvailabilityExceptions.removeAll(thirdShiftUserAvailabilityExceptions);

		for (UserAvailabilityException userAvailabilityException1 : thirdShiftUserAvailabilityExceptions) {
			if (userAvailabilityException1.getStartingTime().equals(dayStartTime)) {
				LocalDate date = userAvailabilityException1.getDate();
				for (UserAvailabilityException userAvailabilityException2 : thirdShiftUserAvailabilityExceptions) {
					if (userAvailabilityException2.getEndingTime().equals(dayEndTime)
							&& userAvailabilityException2.getDate().equals(date.minusDays(1)) && userAvailabilityException2.getType().equals(userAvailabilityException1.getType())) {
						
						UserAvailabilityException mergedUserAvailabilityException = new UserAvailabilityException();
						mergedUserAvailabilityException.setStartingTime(userAvailabilityException2.getStartingTime());
						mergedUserAvailabilityException.setEndingTime(userAvailabilityException1.getEndingTime());
						mergedUserAvailabilityException.setDate(userAvailabilityException2.getDate());
						mergedUserAvailabilityException.setUser(userAvailabilityException2.getUser());
						mergedUserAvailabilityException.setIsActive(userAvailabilityException2.getIsActive());
						mergedUserAvailabilityException.setUuid(userAvailabilityException2.getUuid());
						mergedUserAvailabilityException.setType(userAvailabilityException2.getType());
						
						if(userAvailabilityException1.getBreakStartingTime() != null && userAvailabilityException1.getBreakEndingTime() != null
								&& userAvailabilityException2.getBreakStartingTime() == null && userAvailabilityException2.getBreakEndingTime() == null)
						{
							
							mergedUserAvailabilityException.setBreakStartingTime(userAvailabilityException1.getBreakStartingTime());
							mergedUserAvailabilityException.setBreakEndingTime(userAvailabilityException1.getBreakEndingTime());
							
						}
						else if(userAvailabilityException1.getBreakStartingTime() == null && userAvailabilityException1.getBreakEndingTime() == null
								&& userAvailabilityException2.getBreakStartingTime() != null && userAvailabilityException2.getBreakEndingTime() != null)

						{
							mergedUserAvailabilityException.setBreakStartingTime(userAvailabilityException2.getBreakStartingTime());
							mergedUserAvailabilityException.setBreakEndingTime(userAvailabilityException2.getBreakEndingTime());
						}
						else if(userAvailabilityException1.getBreakStartingTime() != null && userAvailabilityException1.getBreakEndingTime() != null
								&& userAvailabilityException2.getBreakStartingTime() != null && userAvailabilityException2.getBreakEndingTime() != null)
						{	
							mergedUserAvailabilityException.setBreakStartingTime(userAvailabilityException2.getBreakStartingTime());
							mergedUserAvailabilityException.setBreakEndingTime(userAvailabilityException1.getBreakEndingTime());	
						}
						
						mergedUserAvailabilityExceptions.add(mergedUserAvailabilityException);
						unWantedUserAvailabilityExceptions.add(userAvailabilityException1);
						unWantedUserAvailabilityExceptions.add(userAvailabilityException2);
					}
				}
			}
		}
		thirdShiftUserAvailabilityExceptions.removeAll(unWantedUserAvailabilityExceptions);
		userAvailabilityExceptions.addAll(thirdShiftUserAvailabilityExceptions);
		userAvailabilityExceptions.addAll(mergedUserAvailabilityExceptions);

		return userAvailabilityExceptions.stream().map(userAvailabilityException -> userAvailabilityExceptionMapper.mapToDto(userAvailabilityException))
				.collect(Collectors.toList());
	
	
	}

	@Override
	@Transactional
	public void deleteUserAvailabilityException(String userAvailabilityExceptionId) {
		
		LocalTime dayEndTime = LocalTime.of(23, 59);
		LocalTime dayStartTime = LocalTime.of(0, 0);
		LocalTime noon = LocalTime.of(12, 12);

		UserAvailabilityException userAvailabilityException = userAvailabilityExceptionRepository.findByAvailabilityExceptionId(userAvailabilityExceptionId);
		ZonedDateTime now = ZonedDateTime.now(ZoneId.of(userAvailabilityException.getUser().getTimeZone()));
		DayOfWeek day = now.getDayOfWeek();
		LocalTime time = now.toLocalTime();
		LocalDate date = now.toLocalDate();

		if (date.equals(userAvailabilityException.getDate()) && time.isAfter(userAvailabilityException.getStartingTime())
				&& time.isBefore(userAvailabilityException.getEndingTime())) {
			User user = userRepository.findByUserUuid(userAvailabilityException.getUser().getUuid());
			List<OverTime> allOverTime = overTimeRepository.findByUser(user.getUuid());
			if(allOverTime.isEmpty())
			{
				user.setIdleSince(null);
				user.setIsIdleClear(true);
				userRepository.save(user);
			}	
			
		}

		if (userAvailabilityException.getEndingTime().equals(dayEndTime)) {
			List<UserAvailabilityException> nextDayUserAvailabilityExceptions = userAvailabilityExceptionRepository
					.findAllByUserDateAndType(userAvailabilityException.getUser().getUuid(), userAvailabilityException.getDate().plusDays(1), userAvailabilityException.getType());
			for (UserAvailabilityException userAvailabilityException1 : nextDayUserAvailabilityExceptions) {
				if (userAvailabilityException1.getStartingTime().equals(dayStartTime) && userAvailabilityException1.getIsPreviousDayRemainingWork().equals(Boolean.TRUE)) {
					if (date.equals(userAvailabilityException1.getDate())
							&& time.isAfter(userAvailabilityException1.getStartingTime())
							&& time.isBefore(userAvailabilityException1.getEndingTime())) {
						User user = userRepository.findByUserUuid(userAvailabilityException1.getUser().getUuid());
						List<OverTime> allOverTime = overTimeRepository.findByUser(user.getUuid());
						if(allOverTime.isEmpty())
						{
							user.setIdleSince(null);
							user.setIsIdleClear(true);
							userRepository.save(user);
						}
					}

					userAvailabilityExceptionRepository.delete(userAvailabilityException1);
				}
			}
		}
		userAvailabilityExceptionRepository.delete(userAvailabilityException);
	}	

}
