package com.ma.spoton.api.utils;

import com.ma.spoton.api.dtos.UserAuthDto;

import lombok.NoArgsConstructor;

@NoArgsConstructor
public class RoleUtils {

	public static String getRole(UserAuthDto user)
	{
		String role = null; 
		String loggedinUserRole = user.getAuthorities().get(0);
		switch (loggedinUserRole) {
		case "ROLE_IT":
			role = "IT";
			break;
		case "ROLE_ADMIN":
			role = "ADMIN";
			break;
		case "ROLE_DRIVER":
			role = "DRIVER";
			break;
		case "ROLE_SPOTTER":
			role = "SPOTTER";
			break;
		case "ROLE_SUPERVISOR":
			role = "SUPERVISOR";
			break;
		case "ROLE_GUARD":
			role = "GUARD";
			break;
		case "ROLE_CLIENT":
			role = "CLIENT";
			break;
		
		}
		return role;
	}
	
}
