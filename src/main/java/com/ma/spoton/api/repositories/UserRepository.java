package com.ma.spoton.api.repositories;

import com.ma.spoton.api.entities.QUser;
import com.ma.spoton.api.entities.User;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface UserRepository extends JpaRepository<User, Long>, QuerydslPredicateExecutor<User>,
	QuerydslBinderCustomizer<QUser> {

	@Query("select u from User u where u.email = :username and u.isActive=true")
	Optional<User> findActiveUserByEmail(@Param("username") String username);

	Optional<User> findByUuid(String userId);

	@Query("select u from User u where u.uuid = :UserUuid")
	User findByUserUuid(String UserUuid);

	@Query("select u from User u where u.uuid = :userId and u.isActive=true")
	Optional<User> findActiveByUuid(String userId);

	@Query("select u from User u where u.uuid = :userId and u.isActive=true")
	User findOneActiveByUuid(String userId);

	@Query("select u from User u where u.uuid = :userId and u.isActive=false")
	Optional<User> findOneInactiveByUuid(String userId);

	@Query("select u from User u where u.uuid in :userIds and u.isActive=true")
	List<User> findAllActiveByUserIds(List<String> userIds);

	@Query("select u from User u where u.forgotPasswordToken = :forgotPasswordToken and u.isActive=true")
	Optional<User> findActiveUserByForgotPasswordToken(
		@Param("forgotPasswordToken") String forgotPasswordToken);

	@Query("select u from User u inner join u.roles r inner join u.clients c "
		+ "where r.roleName in :rloeNames and c.id = :clientId")
	List<User> findAllByClientAndRoleNames(Long clientId, Set<String> rloeNames);

	@Query("select u from User u inner join u.roles r "
			+ "where r.roleName in :roleNames and u.isActive=true")
	List<User> findAllByRole(Set<String> roleNames);
	
	@Override
	public default void customize(QuerydslBindings bindings, QUser root) {
		bindings.bind(String.class)
			.first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
		bindings.excluding(root.id, root.createdBy, root.lastModifiedBy);
	}

	boolean existsByEmail(@Param("username") String username);

	@Query("SELECT u FROM User u LEFT JOIN FETCH u.roles LEFT JOIN FETCH u.clients WHERE u.uuid = :uuid")
	Optional<User> findByUuidWithRolesAndClients(@Param("uuid") String uuid);
}
