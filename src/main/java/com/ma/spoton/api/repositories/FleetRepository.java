package com.ma.spoton.api.repositories;

import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.Fleet.Type;
import com.ma.spoton.api.entities.QFleet;
import com.ma.spoton.api.entities.Spot;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

@Repository
public interface FleetRepository extends JpaRepository<Fleet, Long>,
	QuerydslPredicateExecutor<Fleet>, QuerydslBinderCustomizer<QFleet> {

	Optional<Fleet> findByUuid(String clientId);

	List<Fleet> findAllByTypeAndUuidIn(Type trailer, List<String> fleetIds);

	@Query("select f from Fleet f where f.uuid = :fleetId and f.isActive=true")
	Optional<Fleet> findActiveByUuid(String fleetId);

	@Query("select f from Fleet f where f.uuid = :fleetId")
	Optional<Fleet> findOneByUuid(String fleetId);

	List<Fleet> findAllByUuidIn(List<String> fleetIds);

	@Query("SELECT f FROM Fleet f WHERE f.unitNumber IN :unitNumbers")
	List<Fleet> findAllByUnitNumber(List<String> unitNumbers);

	Fleet findByUnitNumber(String unitNumber);

	@Query("select f from Fleet f where f.unitNumber = :unitNumber and f.isActive=false")
	Fleet findInactiveByUnitNumber(String unitNumber);

	@Query("select distinct f.carrier from Fleet f Where f.carrier Like Concat('%', :prefix, '%')")
	List<String> findUniqueCarrier(@Param("prefix") String prefix);
	
	@Query("select distinct f.carrier from Fleet f")
    Page<String> findUniqueCarrierPage(Pageable pageable);
	
	@Query("select distinct f.carrier from GuardEntryExit f")
    Page<String> findUniqueCarrierGuardPage(Pageable pageable);
	
	@Query("select distinct f.carrier from GuardEntryExit f Where f.carrier Like Concat('%', :prefix, '%')")
	List<String> findUniqueCarrierGuardEntryExit(@Param("prefix") String prefix);

	@Query("select distinct f.carrier from Fleet f")
	List<String> findUniqueCarrier();
	
	@Query("select distinct f.carrier from GuardEntryExit f")
	List<String> findUniqueCarrierGuardEntryExit();
	
//	@Query("select f from Fleet f where spot = :spot")
	@Query("select f from Fleet f inner join f.spot s where s.id = :spot_id")
	List<Fleet> findAllBySpot(Long spot_id);

	@Override
	public default void customize(QuerydslBindings bindings, QFleet root) {
		bindings.bind(String.class)
			.first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
		bindings.excluding(root.id, root.createdBy, root.lastModifiedBy);
	}

	@Modifying
	@Query(value = "update fleets f SET f.carrier = :carrier WHERE f.id IN (select * from (select f2.id from fleets f2 where f2.carrier in :renameCarriers) as f1)", nativeQuery = true)
//	@Query(value = "UPDATE fleets f SET f.carrier_id = (SELECT c.id FROM carriers c WHERE c.uuid = :carrier) " +
//            "WHERE f.carrier_id IN (SELECT c2.id FROM carriers c2 WHERE c2.uuid IN (:renameCarriers))", nativeQuery = true)
//	@Query(value = "update fleets f SET f.carriers = :carrier WHERE f.id IN (select * from (select f2.id from fleets f2 where f2.carriers in :renameCarriers) as f1)", nativeQuery = true)
	int updateFleetByCarrier(@Param("carrier") String carrier, @Param("renameCarriers") List<String> renameCarriers);

}
