package com.ma.spoton.api.repositories;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import com.ma.spoton.api.entities.OverTime;

@Repository
public interface OverTimeRepository extends JpaRepository<OverTime, Long> , QuerydslPredicateExecutor<OverTime>{

	@Query("select o from OverTime o where o.user.uuid = :userId and o.date = :date")
	OverTime findByUserAndDate(String userId, LocalDate date);
	
	@Query("select o from OverTime o where o.user.uuid = :userId")
	List<OverTime> findByUser(String userId);
	
	@Query("select o from OverTime o join o.user u join u.clients c where o.date = :date and c.uuid = :clientId")
	List<OverTime> findAllByClientAndDate(LocalDate date, String clientId);
	
	@Query("select o from OverTime o join o.user u join u.clients c where o.date = :date and o.endingTime < :presentTime and c.uuid = :clientId")
	List<OverTime> getExpiredOverTimeUsers(LocalDate date, LocalTime presentTime, String clientId);
	
	@Query("select o from OverTime o join o.user u join u.roles r join u.clients c where o.date = :date and o.startingTime < :presentTime and o.endingTime > :presentTime and r.roleName = :roleName and c.uuid = :clientId")
	List<OverTime> getOverTimeUsers(LocalDate date, String roleName, LocalTime presentTime, String clientId);
	
	@Query("select o from OverTime o join o.user u join u.roles r join u.clients c join u.locations l where o.date = :date and o.startingTime < :presentTime and o.endingTime > :presentTime and r.roleName = :roleName and c.uuid = :clientId and l.uuid in :locationIds")
	List<OverTime> getOverTimeSpotters(LocalDate date, String roleName, LocalTime presentTime, String clientId, List<String> locationIds);
	
	@Query("select o from OverTime o where o.user.uuid = :userId and o.date = :date and o.startingTime = :startTime")
	OverTime findByUserDateAndStartTime(String userId, LocalDate date, LocalTime startTime);
	
	
}
