package com.ma.spoton.api.repositories;

import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.ma.spoton.api.entities.Role;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {

  Set<Role> findAllByUuidIn(List<String> roleIds);
  
  List<Role> findAllByOrderByIdAsc();

  @Query("select r from Role r where r.roleName in :roleNames")
  List<Role> findAllByNames(List<String> roleNames);
  
  @Query("SELECT DISTINCT r FROM Client c JOIN c.roles r WHERE c.uuid IN :clientIds AND r.id IN :roleId")
  Role findRolesByClientIds(String clientIds, Long roleId);
  
  @Query("SELECT DISTINCT r FROM Client c JOIN c.roles r WHERE r.id IN :id")
  Role findRoleById(Long id);
  
  @Query("SELECT DISTINCT r FROM Role r " +
	       "WHERE NOT EXISTS (SELECT 1 FROM Client c JOIN c.roles cr WHERE cr = r) " +
	       "ORDER BY r.id ASC")
  List<Role> findAllUnassignedRoles();
  
}
