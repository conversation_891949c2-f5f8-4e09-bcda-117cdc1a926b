package com.ma.spoton.api.mappers;

import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.dtos.UserDto;
import com.ma.spoton.api.dtos.UserExportDto;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.requests.UpdateProfileRequest;
import com.ma.spoton.api.requests.UserRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
    imports = {DateTimeUtils.class, BusinessConstants.class, Objects.class})
public interface UserMapper {

  @Mapping(target = "userId", source = "uuid")
  UserAuthDto mapToAuthDto(User user);

  @Mapping(target = "uuid", source = "userId")
  User mapToUser(UserAuthDto userauthdto);
  
  @Mapping(target = "userId", source = "user.uuid")
  @Mapping(target = "lastLoginTime",
      expression = "java( Objects.nonNull(user.getLastLoginTime()) ? DateTimeUtils.convertZonedDateTimeToString(user.getLastLoginTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, user.getTimeZone()) : \"\" )")
//  @Mapping(target = "idleSince",
//  expression = "java( Objects.nonNull(user.getIdleSince()) ? DateTimeUtils.convertZonedDateTimeToString(user.getIdleSince(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, user.getTimeZone()) : \"\" )")
  @Mapping(target = "clients", ignore = true)
  @Mapping(target = "roles", ignore = true)
  @Mapping(target = "locations", ignore = true)
  UserDto mapToDto(User user);

  User mapToEntity(UserRequest userRequest);

  User updateEntity(UserRequest userRequest, @MappingTarget User user);

  User updateEntity(UpdateProfileRequest updateProfileRequest, @MappingTarget User user);

  @Mapping(target = "userId", source = "user.uuid")
  @Mapping(target = "lastLoginTime",
      expression = "java( Objects.nonNull(user.getLastLoginTime()) ? DateTimeUtils.convertZonedDateTimeToString(user.getLastLoginTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, user.getTimeZone()) : \"\" )")
  @Mapping(target = "roles", ignore = true)
  UserExportDto mapToExportDto(User user, String timeZone);

}
