package com.ma.spoton.api.mappers;

import java.io.IOException;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Value;

import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.GuardEntryExitDto;
import com.ma.spoton.api.dtos.GuardEntryExitExportDto;
import com.ma.spoton.api.entities.GuardEntryExit;
//import com.ma.spoton.api.requests.FleetEntryExitRequest;
import com.ma.spoton.api.utils.DateTimeUtils;
import com.ma.spoton.api.utils.FileUtils;

@Mapper(componentModel = "spring",
    imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class},
    uses = {LocationMapper.class, FleetMapper.class,SpotMapper.class})
public interface GuardEntryExitMapper {
	
  @Mapping(target = "guardEntryExitId", source = "guardEntryExit.uuid")
  @Mapping(target = "audit", ignore = true)
  @Mapping(target = "supplier", source = "guardEntryExit.supplier.supplier")
  @Mapping(target = "dateOfArrival",
  expression = "java( Objects.nonNull(guardEntryExit.getDateOfArrival()) ? DateTimeUtils.convertLocalDateToString(guardEntryExit.getDateOfArrival(), BusinessConstants.DISPLAY_DATE_FORMAT_2) : \"\" )")
  @Mapping(target = "dateOfPickup",
  expression = "java( Objects.nonNull(guardEntryExit.getDateOfPickup()) ? DateTimeUtils.convertLocalDateToString(guardEntryExit.getDateOfPickup(), BusinessConstants.DISPLAY_DATE_FORMAT_2) : \"\" )")
  GuardEntryExitDto mapToDto(GuardEntryExit guardEntryExit);

  @Mapping(target = "guardEntryExitId", source = "fleetEntryExit.uuid")
  @Mapping(target = "locationName", source = "fleetEntryExit.location.locationName")
//  @Mapping(target = "fleetCarrier", source = "fleetEntryExit.fleet.carrier")
  @Mapping(target = "fleetCarrier", source = "fleetEntryExit.fleet.carrier")
  @Mapping(target = "fleetType", source = "fleetEntryExit.fleet.type")
  @Mapping(target = "fleetUnitNumber", source = "fleetEntryExit.fleet.unitNumber")
  @Mapping(target = "spotName", source = "fleetEntryExit.spot.spotName")
  @Mapping(target = "billOfLading", source = "fleetEntryExit.billOfLandingType")
  @Mapping(target = "carrier", source = "fleetEntryExit.carrier")
  @Mapping(target = "supplier", source = "fleetEntryExit.supplier.supplier")
  @Mapping(target = "driverName", source = "fleetEntryExit.driver")
  @Mapping(target = "createdBy", source = "fleetEntryExit.createdBy.email")
  @Mapping(target = "createdDate",
      expression = "java( Objects.nonNull(fleetEntryExit.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(fleetEntryExit.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  @Mapping(target = "lastModifiedBy", source = "fleetEntryExit.lastModifiedBy.email")
  @Mapping(target = "lastModifiedDate",
      expression = "java( Objects.nonNull(fleetEntryExit.getLastModifiedDate()) ? DateTimeUtils.convertZonedDateTimeToString(fleetEntryExit.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  @Mapping(target = "billOfLandingImage", expression = "java(imageURL(fleetEntryExit, cdn))")
  @Mapping(target = "dateOfPickup",
  expression = "java( Objects.nonNull(fleetEntryExit.getDateOfPickup()) ? DateTimeUtils.convertLocalDateToString(fleetEntryExit.getDateOfPickup(), \"MM-dd-yyyy\") : \"\" )")
  @Mapping(target = "dueAtPlant",
  expression = "java( Objects.nonNull(fleetEntryExit.getDueAtPlant()) ? DateTimeUtils.convertLocalDateToString(fleetEntryExit.getDueAtPlant(), \"MM-dd-yyyy\") : \"\" )")
  @Mapping(target = "dateOfArrival",
  expression = "java( Objects.nonNull(fleetEntryExit.getDateOfArrival()) ? DateTimeUtils.convertLocalDateToString(fleetEntryExit.getDateOfArrival(), \"MM-dd-yyyy\") : \"\" )")
  GuardEntryExitExportDto mapToExportDto(GuardEntryExit fleetEntryExit, String timeZone, String cdn);
  
//  GuardEntryExit mapToEntity(FleetEntryExitRequest entryExitRequest);

  default String imageURL(GuardEntryExit fleetEntryExit, String cdn) {
      String imagePath = fleetEntryExit.getBillOfLandingImage();
      
      if (imagePath != null && !imagePath.isEmpty()) {
    	  String newImagePath = cdn + "/" + "EntryExit" + "/" + imagePath;
              return newImagePath;
      }
      return null;  // No image, return null
  }
}
