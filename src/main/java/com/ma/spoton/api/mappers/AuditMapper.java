package com.ma.spoton.api.mappers;

import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.AuditDto;
import com.ma.spoton.api.entities.BaseEntity;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring", imports = {DateTimeUtils.class, BusinessConstants.class},
    uses = UserMapper.class, injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AuditMapper {

  @Mapping(target = "createdDate",
      expression = "java( DateTimeUtils.convertZonedDateTimeToString(baseEntity.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) )")
  @Mapping(target = "lastModifiedDate",
      expression = "java( DateTimeUtils.convertZonedDateTimeToString(baseEntity.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) )")
  AuditDto mapToDto(BaseEntity baseEntity, String timeZone);

}
