package com.ma.spoton.api.mappers;

import java.time.temporal.ChronoUnit;
import java.util.Objects;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.JobDto;
import com.ma.spoton.api.dtos.JobExportDto;
import com.ma.spoton.api.dtos.JobExportWithoutAsnDto;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.requests.JobRequest;
import com.ma.spoton.api.utils.DateTimeUtils;

@Mapper(componentModel = "spring",
        uses = {FleetMapper.class, LocationMapper.class, SpotMapper.class, UserMapper.class},
        imports = {Objects.class, ChronoUnit.class, DateTimeUtils.class, BusinessConstants.class})
public interface JobMapper {

    @Mapping(target = "assignedTo", ignore = true)
    @Mapping(target = "fleet", ignore = true)
    @Mapping(target = "pickupLocation", ignore = true)
    @Mapping(target = "pickupSpot", ignore = true)
    @Mapping(target = "dropLocation", ignore = true)
    @Mapping(target = "dropSpot", ignore = true)
    Job mapToEntity(JobRequest jobRequest);

    @Mapping(target = "assignedTo", ignore = true)
    @Mapping(target = "fleet", ignore = true)
    @Mapping(target = "pickupLocation", ignore = true)
    @Mapping(target = "pickupSpot", ignore = true)
    @Mapping(target = "dropLocation", ignore = true)
    @Mapping(target = "dropSpot", ignore = true)
    @Mapping(target= "bucket", ignore = true)
    Job updateEntity(JobRequest jobRequest, @MappingTarget Job job);

    @Mapping(target = "jobNumber", expression = "java( \"SP\" + job.getId() )")
    @Mapping(target = "jobId", source = "job.uuid")
    @Mapping(target = "audit",
            expression = "java( new AuditMapperImpl(new UserMapperImpl()).mapToDto(job, timeZone) )")
    @Mapping(target = "jobCompletionSeconds", expression = "java( "
                                                           + "Objects.nonNull(job.getPickupDateTime()) && Objects.nonNull(job.getDropDateTime())"
                                                           + " ? ChronoUnit.SECONDS.between(job.getPickupDateTime(), job.getDropDateTime()) : null )")
    @Mapping(target = "pickupDateTime",
            expression = "java( Objects.nonNull(job.getPickupDateTime()) ? DateTimeUtils.convertZonedDateTimeToString(job.getPickupDateTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "dropDateTime",
            expression = "java( Objects.nonNull(job.getDropDateTime()) ? DateTimeUtils.convertZonedDateTimeToString(job.getDropDateTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "pickupLocation", qualifiedByName = {"mapToBasicDto"})
    @Mapping(target = "dropLocation", qualifiedByName = {"mapToBasicDto"})
    @Mapping(target = "createdDate",
            expression = "java( Objects.nonNull(job.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(job.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "scheduleDateTime",
            expression = "java( Objects.nonNull(job.getScheduleDateTime()) ? DateTimeUtils.convertZonedDateTimeToString(job.getScheduleDateTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "isScheduled", expression = "java(job.getIsScheduled() != null && job.getIsScheduled())")
    JobDto mapToDto(Job job, @Context String timeZone);

    @Mapping(target = "jobNumber", expression = "java( \"SP\" + job.getId() )")
    @Mapping(target = "spotCompletionTime", expression = "java( "
                                                         + "Objects.nonNull(job.getPickupDateTime()) && Objects.nonNull(job.getDropDateTime())"
                                                         + " ? DateTimeUtils.formatSeconds(ChronoUnit.SECONDS.between(job.getPickupDateTime(), job.getDropDateTime())) : null )")
    //  @Mapping(target = "fleetCarrier", source = "job.fleet.carriers.carrier")
    @Mapping(target = "fleetCarrier", source = "job.fleet.carrier")
    @Mapping(target = "fleetType", source = "job.fleet.type")
    @Mapping(target = "fleetUnitNumber", source = "job.fleet.unitNumber")
    @Mapping(target = "pickupLocationName", source = "job.pickupLocation.locationName")
    @Mapping(target = "pickupSpotName", source = "job.pickupSpot.spotName")
    @Mapping(target = "pickupSpotType", source = "job.pickupSpot.type")
    @Mapping(target = "pickupDateTime",
            expression = "java( Objects.nonNull(job.getPickupDateTime()) ? DateTimeUtils.convertZonedDateTimeToString(job.getPickupDateTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "dropLocationName", source = "job.dropLocation.locationName")
    @Mapping(target = "dropSpotName", source = "job.dropSpot.spotName")
    @Mapping(target = "dropSpotType", source = "job.dropSpot.type")
    @Mapping(target = "dropDateTime",
            expression = "java( Objects.nonNull(job.getDropDateTime()) ? DateTimeUtils.convertZonedDateTimeToString(job.getDropDateTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "createdDate",
            expression = "java( Objects.nonNull(job.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(job.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  /*@Mapping(target = "lastModifiedDate",
  	expression = "java( Objects.nonNull(job.getLastModifiedDate()) ? DateTimeUtils.convertZonedDateTimeToString(job.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  @Mapping(target = "assigneeFirstName", source = "job.assignedTo.firstName")
  @Mapping(target = "assigneeLastName", source = "job.assignedTo.lastName")
   */
    @Mapping(target = "assignedName",
            expression = "java(Objects.nonNull(job.getAssignedTo()) && Objects.nonNull(job.getAssignedTo().getFirstName()) && Objects.nonNull(job.getAssignedTo().getLastName()) ? job.getAssignedTo().getFirstName() + \" \" + job.getAssignedTo().getLastName() :  \"\" )")
    @Mapping(target = "bolUnsigned", expression = "java(Objects.nonNull(job.getUnsignedBol()) ? \"yes\" : \"no\")")
    @Mapping(target = "bolSigned", expression = "java(Objects.nonNull(job.getSignedBol()) ? \"yes\" : \"no\")")
    @Mapping(target = "createdByUser",
            expression = "java(Objects.nonNull(job.getCreatedBy()) && Objects.nonNull(job.getCreatedBy().getFirstName()) && Objects.nonNull(job.getCreatedBy().getLastName()) ? job.getCreatedBy().getFirstName() + \" \" + job.getCreatedBy().getLastName() :  \"\" )")
    JobExportDto mapToExportDto(Job job, String timeZone);

    @Mapping(target = "jobNumber", expression = "java( \"SP\" + job.getId() )")
    @Mapping(target = "spotCompletionTime", expression = "java( "
                                                         + "Objects.nonNull(job.getPickupDateTime()) && Objects.nonNull(job.getDropDateTime())"
                                                         + " ? DateTimeUtils.formatSeconds(ChronoUnit.SECONDS.between(job.getPickupDateTime(), job.getDropDateTime())) : null )")
    //  @Mapping(target = "fleetCarrier", source = "job.fleet.carriers.carrier")
    @Mapping(target = "fleetCarrier", source = "job.fleet.carrier")
    @Mapping(target = "fleetType", source = "job.fleet.type")
    @Mapping(target = "fleetUnitNumber", source = "job.fleet.unitNumber")
    @Mapping(target = "pickupLocationName", source = "job.pickupLocation.locationName")
    @Mapping(target = "pickupSpotName", source = "job.pickupSpot.spotName")
    @Mapping(target = "pickupSpotType", source = "job.pickupSpot.type")
    @Mapping(target = "pickupDateTime",
            expression = "java( Objects.nonNull(job.getPickupDateTime()) ? DateTimeUtils.convertZonedDateTimeToString(job.getPickupDateTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "dropLocationName", source = "job.dropLocation.locationName")
    @Mapping(target = "dropSpotName", source = "job.dropSpot.spotName")
    @Mapping(target = "dropSpotType", source = "job.dropSpot.type")
    @Mapping(target = "dropDateTime",
            expression = "java( Objects.nonNull(job.getDropDateTime()) ? DateTimeUtils.convertZonedDateTimeToString(job.getDropDateTime(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
    @Mapping(target = "createdDate",
            expression = "java( Objects.nonNull(job.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(job.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  /*@Mapping(target = "lastModifiedDate",
  	expression = "java( Objects.nonNull(job.getLastModifiedDate()) ? DateTimeUtils.convertZonedDateTimeToString(job.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  @Mapping(target = "assigneeFirstName", source = "job.assignedTo.firstName")
  @Mapping(target = "assigneeLastName", source = "job.assignedTo.lastName")
   */
    @Mapping(target = "assignedName",
            expression = "java(Objects.nonNull(job.getAssignedTo()) && Objects.nonNull(job.getAssignedTo().getFirstName()) && Objects.nonNull(job.getAssignedTo().getLastName()) ? job.getAssignedTo().getFirstName() + \" \" + job.getAssignedTo().getLastName() :  \"\" )")
    @Mapping(target = "bolUnsigned", expression = "java(Objects.nonNull(job.getUnsignedBol()) ? \"yes\" : \"no\")")
    @Mapping(target = "bolSigned", expression = "java(Objects.nonNull(job.getSignedBol()) ? \"yes\" : \"no\")")
    JobExportWithoutAsnDto mapToExportWithoutAsnDto(Job job, String timeZone);

}
