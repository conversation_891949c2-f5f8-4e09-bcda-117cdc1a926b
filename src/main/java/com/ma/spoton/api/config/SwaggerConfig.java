package com.ma.spoton.api.config;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.ma.spoton.api.constants.ApplicationConstants;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.security.OAuthFlow;
import io.swagger.v3.oas.models.security.OAuthFlows;
import io.swagger.v3.oas.models.security.Scopes;
import io.swagger.v3.oas.models.security.SecurityScheme;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.AuthorizationScope;
import springfox.documentation.service.Contact;
import springfox.documentation.service.GrantType;
import springfox.documentation.service.OAuth;
import springfox.documentation.service.ResourceOwnerPasswordCredentialsGrant;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class SwaggerConfig {

  public static final String SECURITY_SCHEMA_NAME = "SPOTON-API-AUTH";

  @Resource
  private ApplicationConstants appContsants;

  @Bean
  public Docket api() {
    return new Docket(appContsants.getSwaggerDocVersion() == 3 ? DocumentationType.OAS_30
        : DocumentationType.SWAGGER_2)
            .select().apis(RequestHandlerSelectors.any())
            .paths(PathSelectors
                .ant(appContsants.getContext() + "/" + appContsants.getVersion() + "/**"))
            .build().apiInfo(apiInfo()).securitySchemes(List.of(securitySchema()))
            .securityContexts(Arrays.asList(securityContext()));
  }

  @Bean
  public OpenAPI openApi() {
    OpenAPI openAPI = new OpenAPI().components(new Components());
    openAPI.getComponents().addSecuritySchemes("oauth2", oauth2SecurityScheme());
    return openAPI;
  }

  private ApiInfo apiInfo() {
    return new ApiInfo("SpotOn REST API", "This API is used by A. Blair Clients.", "API TOS",
        "Terms of service",
        new Contact("Administrator", "https://www.ablair-spoton.com", "<EMAIL>"),
        "License of API", "API license URL", Collections.emptyList());
  }

  private AuthorizationScope[] scopes() {
    return new AuthorizationScope[] {new AuthorizationScope("read", "for read operations"),
        new AuthorizationScope("write", "for write operations"),
        new AuthorizationScope("trust", "for access resource APIs")};
  }

  private OAuth securitySchema() {
    GrantType grantType = new ResourceOwnerPasswordCredentialsGrant(
        appContsants.getApiBaseUrl() + appContsants.getContext() + "/oauth/token");
    return new OAuth(SECURITY_SCHEMA_NAME, Arrays.asList(scopes()), List.of(grantType));
  }

  private SecurityContext securityContext() {
    return SecurityContext.builder().securityReferences(defaultAuth()).build();
  }

  private List<SecurityReference> defaultAuth() {
    return List.of(new SecurityReference(SECURITY_SCHEMA_NAME, scopes()));
  }

  private SecurityScheme oauth2SecurityScheme() {
    Scopes scopes = new Scopes();
    scopes.addString("read", "for read operations");
    scopes.addString("write", "for write operations");
    scopes.addString("trust", "for access resource APIs");
    OAuthFlows flows = new OAuthFlows();
    flows.setPassword(new OAuthFlow()
        .authorizationUrl(appContsants.getApiBaseUrl() + appContsants.getContext() + "/oauth/token")
        .scopes(scopes));
    return new SecurityScheme().type(SecurityScheme.Type.OAUTH2).flows(flows);
  }
}
