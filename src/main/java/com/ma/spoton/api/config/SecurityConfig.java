package com.ma.spoton.api.config;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;
import com.ma.spoton.api.constants.ApplicationConstants;

@SuppressWarnings("deprecation")
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

  @Resource
  private UserDetailsService userDetailsService;

  @Resource
  private ApplicationConstants appConstants;
  
  @Bean
  public static PasswordEncoder encoder() {
    return new BCryptPasswordEncoder(6);
  }
  
  @Autowired
  public void globalUserDetails(AuthenticationManagerBuilder auth) throws Exception {
    auth.userDetailsService(userDetailsService).passwordEncoder(encoder());
  }

  @Bean
  public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
    //return super.authenticationManager();
	  return authenticationConfiguration.getAuthenticationManager();
  }

//  @Bean
//  public JwtAccessTokenConverter accessTokenConverter() {
//    JwtAccessTokenConverter converter = new JwtAccessTokenConverter();
//    converter.setSigningKey(appConstants.getJwtSigningKey());
//    converter.setVerifierKey(appConstants.getJwtPublicKey());
//    return converter;
//  }
//
//  @Bean
//  public TokenStore tokenStore() {
//    return new JwtTokenStore(accessTokenConverter());
//  }
//
//  @Bean
//  @Primary // Making this primary to avoid any accidental duplication with another token service
//           // instance of the same name
//  public DefaultTokenServices tokenServices() {
//    DefaultTokenServices defaultTokenServices = new DefaultTokenServices();
//    defaultTokenServices.setTokenStore(tokenStore());
//    defaultTokenServices.setSupportRefreshToken(true);
//    return defaultTokenServices;
//  }

}