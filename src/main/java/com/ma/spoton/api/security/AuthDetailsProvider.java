package com.ma.spoton.api.security;

import static com.ma.spoton.api.constants.BusinessConstants.DEFAULT_TIMEZONE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.springframework.web.context.request.RequestAttributes.SCOPE_SESSION;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import com.ma.spoton.api.constants.SystemRoles;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.utils.SecurityUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@SuppressWarnings({"deprecation"})
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
@Component
public final class AuthDetailsProvider {

  public static UserAuthDto getLoggedInUser() {
    // log.info(" >> getLoggedInUser");
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    return getLoggedInUser(authentication);
  }

  @SuppressWarnings({"unchecked"})
  public static UserAuthDto getLoggedInUser(Authentication authentication) {
    if (isNull(authentication)) {
      return null;
    }

    if (authentication instanceof OAuth2Authentication) {
      OAuth2AuthenticationDetails details =
          (OAuth2AuthenticationDetails) authentication.getDetails();
      String accessTokenValue = details.getTokenValue();
      Map<String, Object> additionalDetails = SecurityUtils.getPayloadFromJwt(accessTokenValue);
      List<String> clientIds = Objects.nonNull(additionalDetails.get("clientIds"))
          ? (List<String>) additionalDetails.get("clientIds")
          : List.of();
      
      log.info(">> Logged In User Details : UserId , UserName, Client Id :({}, {}, {})",additionalDetails.get("userId"), additionalDetails.get("user_name"), additionalDetails.get("client_id"));
      return UserAuthDto.builder()
          .authorities(authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority)
              .collect(Collectors.toList()))
          .clientIds(clientIds)
          .id(Long.parseLong(SecurityUtils.decode((String) additionalDetails.get("id"), 6)))
          .userId((String) additionalDetails.get("userId"))
          .email((String) additionalDetails.get("email"))
          .phone((String) additionalDetails.get("phone"))
          .firstName((String) additionalDetails.get("firstName"))
          .lastName((String) additionalDetails.get("lastName"))
          .timeZone((String) additionalDetails.get("timeZone")).build();
    }

    return null;
  }

  public static String getTimeZone() {
    UserAuthDto user = getLoggedInUser();
    return nonNull(user) ? user.getTimeZone() : DEFAULT_TIMEZONE;
  }

  public static ServletRequestAttributes getSession() {
    log.debug(" >> getSession");
    return (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
  }

  public static void setSessionAttribute(String attributeName, Object attributeValue) {
    log.debug(" >> setSessionAttribute({}, {})", attributeName, attributeValue);
    getSession().setAttribute(attributeName, attributeValue, SCOPE_SESSION);
  }

  public static Object getSessionAttribute(String attributeName) {
    Object attributeValue = getSession().getAttribute(attributeName, SCOPE_SESSION);
    log.debug("Value Retrieved From Session : {}", attributeValue);
    return attributeValue;
  }

  public static boolean isRolePresent(String role) {
    log.debug(">> isRolePresent({})", role);
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (isNull(authentication)) {
      return false;
    }
    return authentication.getAuthorities().stream()
        .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(role));
  }

  public static boolean hasAnyClientRole(Authentication authentication, String clientId,
      String[] roles) {
    if (authentication.getAuthorities().stream()
        .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(SystemRoles.IT)
            || grantedAuthority.getAuthority().equals(SystemRoles.ADMIN))) {
      return true;
    }

    Set<String> roleSet =
        Objects.nonNull(roles) ? Stream.of(roles).collect(Collectors.toSet()) : Set.of();
    boolean isRolePresent = authentication.getAuthorities().stream()
        .map(GrantedAuthority::getAuthority).anyMatch(roleSet::contains);

    UserAuthDto user = getLoggedInUser(authentication);
    if (isRolePresent && nonNull(user)) {
      return user.getClientIds().contains(clientId);
    }
    return false;
  }

}
