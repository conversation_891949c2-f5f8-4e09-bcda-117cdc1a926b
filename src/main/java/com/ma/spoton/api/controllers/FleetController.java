package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import java.net.URI;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.ConstraintViolationException;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ma.spoton.api.dtos.FleetDto;
import com.ma.spoton.api.dtos.GuardEntryExitDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.TrailerLogDto;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.GuardEntryExit;
import com.ma.spoton.api.entities.Job;
import com.ma.spoton.api.entities.TrailerLog;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.FleetRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.FleetService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Fleets APIs")
@RestController
@RequestMapping("/v1/fleets")
public class FleetController {

  @Autowired
  private FleetService fleetService;

  @Value("${application.api-base-url}")
  private String apiBaseUrl;

  @Value("${application.version}")
  private String version;
  
  private static final Logger LOGGER = LoggerFactory.getLogger(FleetController.class);

  @Secured({IT, ADMIN, GUARD, CLIENT, SUPERVISOR, DRIVER, SPOTTER})
  @ApiOperation(value = "This API is used to create new Fleet", code = 201,
      consumes = "application/json")
  @PostMapping
  public ResponseEntity<FleetDto> createFleet(@Valid @RequestBody FleetRequest fleetRequest) {
    
	long start = System.currentTimeMillis();
    FleetDto fleet = fleetService.createFleet(fleetRequest,
        AuthDetailsProvider.getLoggedInUser().getClientIds());
    LoggerUtil.logSLA(LOGGER, "createFleet", start, "createFleet completed");
    return ResponseEntity
        .created(
            URI.create(String.format("%s/%s/fleets/%s", apiBaseUrl, version, fleet.getFleetId())))
        .body(fleet);
  }

  @ApiOperation("This API is used to update Fleet")
  @PutMapping("/{fleetId}")
  @Secured({IT, ADMIN, SUPERVISOR, CLIENT})
  public ResponseEntity<Void> updateFleet(@PathVariable String fleetId,
      @Valid @RequestBody FleetRequest fleetRequest) {
	long start = System.currentTimeMillis();
    try {
      fleetService.updateFleet(fleetId, fleetRequest);
    } catch (ConstraintViolationException | DataIntegrityViolationException e) {
      log.error("Error occurred while updating fleet! Reason : {}", e.getMessage());
      throw new ServiceException(ErrorCode.DUPLICATE_FLEET_UNIT_NUMBER);
    }
    LoggerUtil.logSLA(LOGGER, "updateFleet", start, "updateFleet completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

  @ApiOperation("This API is used to delete Fleet")
  @DeleteMapping("/{fleetId}")
  @Secured({IT, ADMIN, SUPERVISOR})
  public ResponseEntity<Void> deleteFleet(@PathVariable String fleetId) {
	long start = System.currentTimeMillis();
	
    fleetService.deleteFleet(fleetId);
    LoggerUtil.logSLA(LOGGER, "deleteFleet", start, "deleteFleet completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

  @ApiOperation("This API is used to get Fleet by ID")
  @GetMapping("/{fleetId}")
  @PreAuthorize("isAuthenticated()")
  public ResponseEntity<FleetDto> getFleet(@PathVariable String fleetId) {
	long start = System.currentTimeMillis();
	  
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    FleetDto resource = fleetService.getFleet(fleetId, user.getTimeZone(), user.getClientIds());
    LoggerUtil.logSLA(LOGGER, "getFleet", start, "getFleet completed");
    return ResponseEntity.ok(resource);
    /*return ResponseEntity
        .ok(fleetService.getFleet(fleetId, user.getTimeZone(), user.getClientIds()));*/
  }

  @ApiOperation("This API is used to get paginated list of Fleets")
  @GetMapping
  @PreAuthorize("isAuthenticated()")
  public ResponseEntity<PagedResponse<FleetDto>> getFleets(
      @QuerydslPredicate(root = Fleet.class) Predicate predicate, Pageable pageable,
      @RequestParam(required = false) String locationIds, @RequestParam(required = false) String unit_sequenceNumber) {
	long start = System.currentTimeMillis();
	
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

    PagedResponse<FleetDto> resource = fleetService.getFleets(predicate, pageable, user.getTimeZone(), user.getClientIds(), locationIds, unit_sequenceNumber);
    LoggerUtil.logSLA(LOGGER, "getFleets", start, "getFleets completed");
    return ResponseEntity.ok(resource);
    /*return ResponseEntity
        .ok(fleetService.getFleets(predicate, pageable, user.getTimeZone(), user.getClientIds()));*/
  }

  @ApiOperation("This API is used to get paginated list of Fleet Entry/Exit records")
  @GetMapping("/entryExits")
  @Secured({IT, ADMIN, CLIENT, GUARD, SUPERVISOR})
  public ResponseEntity<PagedResponse<GuardEntryExitDto>> getFleetEntryExits(
		  @RequestParam(required = false) String fromDate, @RequestParam(required = false) String toDate,
      @QuerydslPredicate(root = GuardEntryExit.class) Predicate predicate, Pageable pageable) {
    
	long start = System.currentTimeMillis();
	
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    
    PagedResponse<GuardEntryExitDto> resource = fleetService.getFleetEntryExits(predicate, pageable,
            user.getTimeZone(), user.getClientIds(), fromDate, toDate);
    LoggerUtil.logSLA(LOGGER, "getFleetEntryExits", start, "getFleetEntryExits completed");
    return ResponseEntity.ok(resource);
    /*return ResponseEntity.ok(fleetService.getFleetEntryExits(predicate, pageable,
        user.getTimeZone(), user.getClientIds()));
    */
  }

  @ApiOperation("This API is used to export list of Fleets as CSV")
  @PreAuthorize("isAuthenticated()")
  @GetMapping("/export/csv")
  public ResponseEntity<Resource> exportFleetsAsCsv(
      @QuerydslPredicate(root = Fleet.class) Predicate predicate) {
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    Resource resource =
        fleetService.exportFleetsAsCSV(predicate, user.getTimeZone(), user.getClientIds());
    LoggerUtil.logSLA(LOGGER, "exportFleetsAsCsv", start, "exportFleetsAsCsv completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "SpotOn_Fleets.csv")
        .contentType(MediaType.parseMediaType("text/csv")).body(resource);
  }
  
  
  @ApiOperation("This API is used to export list of Fleets as EXCEL")
  @PreAuthorize("isAuthenticated()")
  @GetMapping("/export/excel")
  public ResponseEntity<Resource> exportFleetsAsExcel(
      @QuerydslPredicate(root = Fleet.class) Predicate predicate,
      @RequestParam(required = false) String locationIds) {
	long start = System.currentTimeMillis();
	
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    Resource resource =
        fleetService.exportFleetsAsEXCEL(predicate, user.getTimeZone(), user.getClientIds(),locationIds);
    LoggerUtil.logSLA(LOGGER, "exportFleetsAsExcel", start, "exportFleetsAsExcel completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Asset Inventory_Report.xlsx")
        .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
  }
  
  @ApiOperation("This API is used to export list of Fleets as PDF")
  @PreAuthorize("isAuthenticated()")
  @GetMapping("/export/pdf")
  public ResponseEntity<Resource> exportFleetsAsPDF(
      @QuerydslPredicate(root = Fleet.class) Predicate predicate,
      @RequestParam(required = false) String locationIds) {
	long start = System.currentTimeMillis();
	
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    Resource resource =
        fleetService.exportFleetsAsPDF(predicate, user.getTimeZone(), user.getClientIds(),locationIds);
    LoggerUtil.logSLA(LOGGER, "exportFleetsAsPDF", start, "exportFleetsAsPDF completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Asset Inventory_Report.pdf")
        .contentType(MediaType.parseMediaType("text/pdf")).body(resource);
  }
  

  @ApiOperation("This API is used to export list of Fleet Entry Exits as CSV")
  @Secured({IT, ADMIN, CLIENT, GUARD, SUPERVISOR})
  @GetMapping("/entryExits/export/csv")
  public ResponseEntity<Resource> exportFleetEntryExitsAsCsv(
      @QuerydslPredicate(root = GuardEntryExit.class) Predicate predicate) {
	long start = System.currentTimeMillis();
	
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    Resource resource =
        fleetService.exportFleetEntryExitsAsCSV(predicate, user.getTimeZone(), user.getClientIds());
    LoggerUtil.logSLA(LOGGER, "exportFleetEntryExitsAsCsv", start, "exportFleetEntryExitsAsCsv completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=" + "SpotOn_FleetEntryExits.csv")
        .contentType(MediaType.parseMediaType("text/csv")).body(resource);
  }
  
  @ApiOperation("This API is used to export list of Fleet Entry Exits as EXCEL")
  @Secured({IT, ADMIN, CLIENT, GUARD, SUPERVISOR})
  @GetMapping("/entryExits/export/excel")
  public ResponseEntity<Resource> exportFleetEntryExitsAsExcel(@RequestParam(required = false) String fromDate,
	      @RequestParam(required = false) String toDate,
	      @QuerydslPredicate(root = GuardEntryExit.class) Predicate predicate) {
	long start = System.currentTimeMillis();
	
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    Resource resource =
        fleetService.exportFleetEntryExitsAsEXCEL(predicate, user.getTimeZone(), user.getClientIds(), fromDate, toDate);
    
    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
    
    try
    {
    	if(fromDate != null)
    	{
    		Date date1 = inputFormat.parse(fromDate);
       	    fromDate = outputFormat.format(date1);
    	}
    	 
    	 if(toDate != null)
    	 {
    		Date date2 = inputFormat.parse(toDate);
        	toDate = outputFormat.format(date2);
    	 }
    	
    }
    catch (ParseException e) {
        e.printStackTrace();
    }
    String fileName = "Entry_Exit_Details_Report_";
    if(fromDate != null && toDate != null)
    {
     fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
    }
     fileName = fileName.concat(".xlsx");

    LoggerUtil.logSLA(LOGGER, "exportFleetEntryExitsAsExcel", start, "exportFleetEntryExitsAsExcel completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=" + fileName)
        .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
  }
  
  
  @ApiOperation("This API is used to export list of Fleet Entry Exits as EXCEL")
  @Secured({IT, ADMIN, CLIENT, GUARD, SUPERVISOR})
  @GetMapping("/entryExits/report/export/excel")
  public ResponseEntity<Resource> exportEntryExitsReportAsExcel(@RequestParam(required = false) String fromDate,
	      @RequestParam(required = false) String toDate,
	      @QuerydslPredicate(root = GuardEntryExit.class) Predicate predicate) {
    //log.info(">> exportFleetEntryExits({})", predicate);
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    Resource resource =
        fleetService.exportEntryExitsReportAsEXCEL(predicate, user.getTimeZone(), user.getClientIds(), fromDate, toDate);
    
    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
    
    try
    {
    	if(fromDate != null)
    	{
    		Date date1 = inputFormat.parse(fromDate);
       	    fromDate = outputFormat.format(date1);
    	}
    	 
    	 if(toDate != null)
    	 {
    		Date date2 = inputFormat.parse(toDate);
        	toDate = outputFormat.format(date2);
    	 }
    	
    }
    catch (ParseException e) {
        e.printStackTrace();
    }
    String fileName = "Entry_Exit_Details_Report_";
    if(fromDate != null && toDate != null)
    {
     fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
    }
     fileName = fileName.concat(".xlsx");
    
    LoggerUtil.logSLA(LOGGER, "exportEntryExitsReportAsExcel", start, "exportEntryExitsReportAsExcel completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=" + fileName)
        .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
  }
  
  
  
  @ApiOperation("This API is used to export list of Fleet Entry Exits as EXCEL")
  @Secured({IT, ADMIN, CLIENT, GUARD, SUPERVISOR})
  @GetMapping("/entryExits/report/export/pdf")
  public ResponseEntity<Resource> exportEntryExitsReportAsPdf(@RequestParam(required = false) String fromDate,
	      @RequestParam(required = false) String toDate,
	      @QuerydslPredicate(root = GuardEntryExit.class) Predicate predicate) {
    //log.info(">> exportFleetEntryExits({})", predicate);
	
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    Resource resource =
        fleetService.exportEntryExitsReportAsPDF(predicate, user.getTimeZone(), user.getClientIds(), fromDate, toDate);
    
    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
    
    try
    {
    	if(fromDate != null)
    	{
    		Date date1 = inputFormat.parse(fromDate);
       	    fromDate = outputFormat.format(date1);
    	}
    	 
    	 if(toDate != null)
    	 {
    		Date date2 = inputFormat.parse(toDate);
        	toDate = outputFormat.format(date2);
    	 }
    	
    }
    catch (ParseException e) {
        e.printStackTrace();
    }
    String fileName = "Entry_Exit_Details_Report_";
    if(fromDate != null && toDate != null)
    {
     fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
    }
     fileName = fileName.concat(".pdf");
    
    LoggerUtil.logSLA(LOGGER, "exportEntryExitsReportAsPdf", start, "exportEntryExitsReportAsPdf completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=" + fileName)
        .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
  }
  
  
  
  
  @ApiOperation("This API is used to deactivate Fleet")
  @PatchMapping("/{fleetId}/deactivate")
  @Secured({IT, ADMIN, SUPERVISOR})
  public ResponseEntity<Void> deactivateFleet(@PathVariable String fleetId) {
	long start = System.currentTimeMillis();
	
    fleetService.deactivateFleet(fleetId);
    LoggerUtil.logSLA(LOGGER, "deactivateFleet", start, "deactivateFleet completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

  @ApiOperation("This API is used to activate Fleet")
  @PatchMapping("/{fleetId}/activate")
  @Secured({IT, ADMIN, SUPERVISOR})
  public ResponseEntity<Void> activateFleet(@PathVariable String fleetId) {
	long start = System.currentTimeMillis();
	
    fleetService.activateFleet(fleetId);
    LoggerUtil.logSLA(LOGGER, "activateFleet", start, "activateFleet completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }
  
  @ApiOperation("This API is used to get paginated list of Trailer history logs")
  @GetMapping("/history")
  @PreAuthorize("isAuthenticated()")
  public ResponseEntity<PagedResponse<TrailerLogDto>> getTrailerLogs(@RequestParam(required = false) String fromDate, @RequestParam(required = false) String toDate,
      @QuerydslPredicate(root = TrailerLog.class) Predicate predicate, Pageable pageable) {
	long start = System.currentTimeMillis();
	
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();

    PagedResponse<TrailerLogDto> resource = fleetService.getTrailerLogs(predicate, pageable, user.getTimeZone(), user.getClientIds(), fromDate, toDate);
    LoggerUtil.logSLA(LOGGER, "getFleets", start, "getFleets completed");
    return ResponseEntity.ok(resource);
  }
  
  @ApiOperation("This API is used to export list of Moves as EXCEL")
  @PreAuthorize("isAuthenticated()")
  @GetMapping("/history/export/excel")
  public ResponseEntity<Resource> exportTrailerHistoryAsExcel(@RequestParam(required = false) String fromDate,
      @RequestParam(required = false) String toDate,
      @QuerydslPredicate(root = TrailerLog.class) Predicate predicate) {
	
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    
    Resource resource = fleetService.exportTrailerHistorysAsEXCEL(fromDate, toDate, predicate, user.getTimeZone());
    
    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
    
    try
    {
    	if(fromDate != null)
    	{
    		Date date1 = inputFormat.parse(fromDate);
       	    fromDate = outputFormat.format(date1);
    	}
    	 
    	 if(toDate != null)
    	 {
    		Date date2 = inputFormat.parse(toDate);
        	toDate = outputFormat.format(date2);
    	 }
    	
    }
    catch (ParseException e) {
        e.printStackTrace();
    }
    
    String fileName = "Trailer_History_";
    if(fromDate != null && toDate != null)
    {
     fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
    }
     fileName = fileName.concat(".xlsx");
     LoggerUtil.logSLA(LOGGER, "exportTrailerHistoryAsExcel", start, "exportTrailerHistoryExcel completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
        .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
  }
  
  @ApiOperation("This API is used to export list of Moves as Pdf")
  @PreAuthorize("isAuthenticated()")
  @GetMapping("/history/export/pdf")
  public ResponseEntity<Resource> exportTrailerHistoryAsPdf(@RequestParam(required = false) String fromDate,
      @RequestParam(required = false) String toDate, @QuerydslPredicate(root = TrailerLog.class) Predicate predicate) {
	
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    
    Resource resource = fleetService.exportTrailerHistoryAsPDF(fromDate, toDate, predicate, user.getTimeZone());
    
    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat outputFormat = new SimpleDateFormat("MM-dd-yyyy");
    
    try
    {
    	if(fromDate != null)
    	{
    		Date date1 = inputFormat.parse(fromDate);
       	    fromDate = outputFormat.format(date1);
    	}
    	 
    	 if(toDate != null)
    	 {
    		Date date2 = inputFormat.parse(toDate);
        	toDate = outputFormat.format(date2);
    	 }
    	
    }
    catch (ParseException e) {
        e.printStackTrace();
    }
    
    String fileName = "Trailer_History_";
    if(fromDate != null && toDate != null)
    {
     fileName = fileName.concat(fromDate).concat("_to_").concat(toDate);
    }
     fileName = fileName.concat(".pdf");
     LoggerUtil.logSLA(LOGGER, "trailerHistoryAsPdf", start, "trailerHistoryAsPdf completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
        .contentType(MediaType.parseMediaType("text/pdf")).body(resource);
  }
  
}
