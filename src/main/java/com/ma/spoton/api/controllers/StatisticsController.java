package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ma.spoton.api.dtos.JobStatisticsDto;
import com.ma.spoton.api.dtos.MessageStatisticsDto;
import com.ma.spoton.api.dtos.TotalStatisticsDto;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.StatisticsService;
import com.ma.spoton.api.services.UserService;
import com.ma.spoton.api.utils.LoggerUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Clients APIs")
@RestController
@RequestMapping("/v1/statistics")
public class StatisticsController {

  @Autowired
  private UserService userService;
	
  @Autowired
  private StatisticsService statisticsService;
  
  private static final Logger LOGGER = LoggerFactory.getLogger(StatisticsController.class);

  @ApiOperation(
      value = "This API is used to get Total Statistics like total clients, users, fleets etc.")
  @Secured({IT, ADMIN, CLIENT, SUPERVISOR})
  @GetMapping("/totals")
  public ResponseEntity<TotalStatisticsDto> getTotals() {
	 
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
	userService.updateLastActiveTime(user.getUserId());
	TotalStatisticsDto resource = statisticsService.getTotals(AuthDetailsProvider.getLoggedInUser().getClientIds());
	LoggerUtil.logSLA(LOGGER, "getTotals", start, "getTotals completed");
	return ResponseEntity.ok(resource);
	/*return ResponseEntity
        .ok(statisticsService.getTotals(AuthDetailsProvider.getLoggedInUser().getClientIds()));*/
  }

  @ApiOperation(
      value = "This API is used to Jobs Statistics either total or between provided date time range.")
  @Secured({IT, ADMIN, CLIENT, SUPERVISOR})
  @GetMapping("/jobs")
  public ResponseEntity<JobStatisticsDto> getJobStatistics(
      @RequestParam(required = false) String timePeriod,
      @RequestParam(required = false) Optional<String> clientId) {
	
	long start = System.currentTimeMillis();
    ZonedDateTime now =ZonedDateTime.now();
    ZonedDateTime from =null;
    if(timePeriod != null)
    {
        switch (timePeriod) {
    	case "24h":
    		from = now.minusHours(24);
    		break;
    	case "7d":
    		from = now.minusDays(7);
    		break;
    	case "30d":
    		from = now.minusDays(30);
    		break;
    	case "yearToDate":
    		from = now
            .withDayOfYear(1)
            .withMonth(1)
            .withHour(0)
            .withMinute(0)
            .withSecond(0)
            .withNano(0);
    	
    	}
    }
    else
    {
    	from = now.minusHours(24);
    }

   
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    Optional<String> toDateTime = Optional.ofNullable(now)
                                .map(dateTime -> dateTime.format(formatter))
                                .map(Optional::of)
                                .orElse(Optional.empty());
    Optional<String> fromDateTime = Optional.ofNullable(from)
            .map(dateTime -> dateTime.format(formatter))
            .map(Optional::of)
            .orElse(Optional.empty());
    
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    List<String> clientIds = user.getClientIds();
    if (clientId.isPresent()) {
    if(user.getAuthorities().contains(IT) || user.getAuthorities().contains(ADMIN))
    {
    	 clientIds = List.of(clientId.get());
    }
    else
    {
    	 if (clientIds.contains(clientId.get())) {
    	        clientIds = List.of(clientId.get());
    	      } else {
    	        throw new ServiceException(ErrorCode.CLIENT_ACCESS_DENIED);
    	      }	
    }
     
    }
    TimeZone serverTimeZone = TimeZone.getDefault();
    JobStatisticsDto resource = statisticsService.getJobStatistics(fromDateTime, toDateTime,
            serverTimeZone.getID(), clientIds);
    LoggerUtil.logSLA(LOGGER, "getJobStatistics", start, "getJobStatistics completed");
    return ResponseEntity.ok(resource);
    /*return ResponseEntity.ok(statisticsService.getJobStatistics(fromDateTime, toDateTime,
        serverTimeZone.getID(), clientIds));*/
  }

  @ApiOperation(
      value = "This API is used to Messages Statistics either total or between provided date time range.")
  @PreAuthorize("isAuthenticated()")
  @GetMapping("/messages")
  public ResponseEntity<MessageStatisticsDto> getMessageStatistics(
      @RequestParam(required = false) Optional<String> fromDateTime,
      @RequestParam(required = false) Optional<String> toDateTime) {

	long start = System.currentTimeMillis();    
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    MessageStatisticsDto resource = statisticsService.getMessageStatistics(fromDateTime, toDateTime,
            user.getTimeZone(), user.getUserId());
    LoggerUtil.logSLA(LOGGER, "getMessageStatistics", start, "getMessageStatistics completed");
    return ResponseEntity.ok(resource);
    /*return ResponseEntity.ok(statisticsService.getMessageStatistics(fromDateTime, toDateTime,
        user.getTimeZone(), user.getUserId()));*/
  }

}
