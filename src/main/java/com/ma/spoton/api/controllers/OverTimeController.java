package com.ma.spoton.api.controllers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.OverTimeDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.entities.OverTime;
import com.ma.spoton.api.requests.OverTimeRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.OverTimeService;
import com.ma.spoton.api.services.UserService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.ADMIN;

@Slf4j
@Api("Trailer Audit api")
@RestController
@RequestMapping("/v1/overTime")
public class OverTimeController {

//	@Autowired
//	private UserService userService;
	
	@Autowired
	private OverTimeService overTimeService;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(OverTimeController.class);
	
	  @ApiOperation(value = "This API is used to add over time for users", code = 200)
	  @PostMapping
	  private ResponseEntity<Void> addUserOverTime(@RequestBody OverTimeRequest overTimeRequest)
	  {
		  long start = System.currentTimeMillis();
		  overTimeService.addOvertimeForUser(overTimeRequest.getUserId());
		  LoggerUtil.logSLA(LOGGER, "addUserOverTime", start, "addUserOverTime completed");
		  return ResponseEntity.ok().build();
	  }
	  
	  
	  @DeleteMapping("/{userId}")
	  private ResponseEntity<Void> deleteUserOverTime(@PathVariable String userId)
	  {
		  long start = System.currentTimeMillis();
		  overTimeService.deleteOverTimeForUser(userId);
		  LoggerUtil.logSLA(LOGGER, "deleteUserOverTime", start, "deleteUserOverTime completed");
		 return null; 
	  }
	  
	  @GetMapping
	  private ResponseEntity<PagedResponse<OverTimeDto>> getOverTimeUsers(@QuerydslPredicate(root = OverTime.class) Predicate predicate, Pageable pageable)
	  {
		  long start = System.currentTimeMillis();
		  String timeZone= null;
		  UserAuthDto user = AuthDetailsProvider.getLoggedInUser();		 
	      timeZone = user.getTimeZone();
	      PagedResponse<OverTimeDto> resource = overTimeService.getOverTimeUsers(timeZone, predicate, pageable);
	      LoggerUtil.logSLA(LOGGER, "getOverTimeUsers", start, "getOverTimeUsers completed");
	      return ResponseEntity.ok(resource);
		  /*return ResponseEntity
			        .ok(overTimeService.getOverTimeUsers(timeZone, predicate, pageable));*/
	  }
	
}
