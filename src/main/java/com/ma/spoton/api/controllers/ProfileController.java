package com.ma.spoton.api.controllers;

import java.util.List;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.dtos.UserDto;
import com.ma.spoton.api.requests.ForgotPasswordRequest;
import com.ma.spoton.api.requests.ResetPasswordRequest;
import com.ma.spoton.api.requests.UpdateProfileRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.UserService;
import com.ma.spoton.api.utils.LoggerUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(value = "Profile APIs")
@RestController
@RequestMapping("/v1/profile")
public class ProfileController {

  @Autowired
  private UserService userService;
  
  private static final Logger LOGGER = LoggerFactory.getLogger(ProfileController.class);

  @PreAuthorize("isAuthenticated()")
  @ApiOperation(value = "This API is used to get User Profile.")
  @GetMapping
  public ResponseEntity<UserDto> getMyProfile() {
	  
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    UserDto resource = userService.getUser(user.getUserId(), user.getTimeZone(), List.of());
    LoggerUtil.logSLA(LOGGER, "getMyProfile", start, "getMyProfile completed");
    return ResponseEntity.ok(resource);
    //return ResponseEntity.ok(userService.getUser(user.getUserId(), user.getTimeZone(), List.of()));
  }

  @PreAuthorize("isAuthenticated()")
  @ApiOperation(value = "This API is used to update logged in user Profile.", code = 204)
  @PutMapping
  public ResponseEntity<Void> updateMyProfile(
      @Valid @RequestBody UpdateProfileRequest updateProfileRequest) {
	
	long start = System.currentTimeMillis();
    userService.updateUserProfile(AuthDetailsProvider.getLoggedInUser().getUserId(),
        updateProfileRequest);
    
    LoggerUtil.logSLA(LOGGER, "updateMyProfile", start, "updateMyProfile completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

  @ApiOperation(value = "This API is used to generate forgot password token.", code = 204)
  @PutMapping("/forgotPassword")
  public ResponseEntity<Void> createforgotPasswordToken(
      @Valid @RequestBody ForgotPasswordRequest forgotPasswordRequest) {
	
	long start = System.currentTimeMillis();
	
    userService.createforgotPasswordToken(forgotPasswordRequest);
    
    LoggerUtil.logSLA(LOGGER, "createforgotPasswordToken", start, "createforgotPasswordToken completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

  @ApiOperation(value = "This API is used to generate reset password token.", code = 204)
  @PutMapping("/resetPassword")
  public ResponseEntity<Void> createResetPassword(
      @Valid @RequestBody ResetPasswordRequest resetPasswordRequest) {
	
	long start = System.currentTimeMillis();
    userService.createResetPassword(resetPasswordRequest);
    LoggerUtil.logSLA(LOGGER, "createResetPassword", start, "createResetPassword completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

}
