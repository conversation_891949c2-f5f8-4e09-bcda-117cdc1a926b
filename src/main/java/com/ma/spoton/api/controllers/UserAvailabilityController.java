package com.ma.spoton.api.controllers;

import java.net.URI;
import java.util.List;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.UserAvailabilityDto;
import com.ma.spoton.api.entities.UserAvailability;
import com.ma.spoton.api.requests.ActivateUserAvailabilityRequest;
import com.ma.spoton.api.requests.UserAvailabilityRequest;
import com.ma.spoton.api.services.UserAvailabilityService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("UserAvailability api")
@RestController
@RequestMapping("/v1/userAvailability")
public class UserAvailabilityController {

	@Value("${application.api-base-url}")
	private String apiBaseUrl;

	@Value("${application.version}")
	private String version;
	
	@Autowired
	private UserAvailabilityService userAvailabilityService;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(UserAvailabilityController.class);
	
	  @PreAuthorize("isAuthenticated()")
	  @ApiOperation(value = "This API is used to create new User Availability", code = 201,
	      consumes = "application/json")
	  @PostMapping
	  public ResponseEntity<Void> createUserAvailability(@Valid @RequestBody UserAvailabilityRequest[] userAvailabilityRequests) {
		  long start = System.currentTimeMillis();
		  for(UserAvailabilityRequest userAvailabilityRequest : userAvailabilityRequests)
		  {
			  UserAvailability userAvailability = userAvailabilityService.addUserAvailability(userAvailabilityRequest);  
		  }
		  LoggerUtil.logSLA(LOGGER, "createUserAvailability", start, "createUserAvailability completed");
	    return ResponseEntity
	            .created(URI.create(String.format("%s/%s/jobs/", apiBaseUrl, version)))
	            .build(); 
	  }
	  
	  
	  @PreAuthorize("isAuthenticated()")
	  @ApiOperation("This API is used to get Client Availability by ID")
	  @GetMapping("/{userId}")
	  //@PreAuthorize("isAuthenticated()")
	  public ResponseEntity<List<UserAvailabilityDto>> getUserAvailability(@PathVariable String userId,
			  @QuerydslPredicate(root = UserAvailability.class) Predicate predicate, Pageable pageable) {
		  long start = System.currentTimeMillis();
		  List<UserAvailabilityDto> resource= userAvailabilityService
	        .getUserAvailability(userId);
		  LoggerUtil.logSLA(LOGGER, "getUserAvailability", start, "getUserAvailability completed");
		  return ResponseEntity.ok(resource);
		  /*return ResponseEntity.ok(userAvailabilityService
			        .getUserAvailability(userId));*/
	
	  }
	  
	  
	  @PreAuthorize("isAuthenticated()")
	  @ApiOperation(value = "This API is used to delete user availability", code = 204)
	  @DeleteMapping("/{userAvailabilityId}")
	  public ResponseEntity<Void> deleteUserAvailability(@PathVariable String userAvailabilityId) {
	
	   long start = System.currentTimeMillis();
	   userAvailabilityService.deleteUserAvailability(userAvailabilityId);
	   LoggerUtil.logSLA(LOGGER, "deleteUserAvailability", start, "deleteUserAvailability completed");
	    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
	  }
	  
	  @PreAuthorize("isAuthenticated()")
	  @ApiOperation(value = "This API is used to activate/dectivate user availability", code = 204)
	  @PutMapping("/{userAvailabilityId}")
	  public ResponseEntity<Void> updateUserAvailability(@PathVariable String userAvailabilityId
			  ,@Valid @RequestBody ActivateUserAvailabilityRequest activateUserAvailabilityRequest){
		  long start = System.currentTimeMillis();
		  userAvailabilityService.activateDeactivateUserAvailability(userAvailabilityId, activateUserAvailabilityRequest.getActive());
		  LoggerUtil.logSLA(LOGGER, "updateUserAvailability", start, "updateUserAvailability completed");
		  return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
	  }
	
	
}
