package com.ma.spoton.api.controllers;

import com.ma.spoton.api.dtos.DockTurnAroundTimeDTO;
import com.ma.spoton.api.dtos.LocationDwellTimeDTO;
import com.ma.spoton.api.services.DockTurnAroundTimeService;
import com.ma.spoton.api.utils.LoggerUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/turn-around-time")
public class TurnAroundTimeController {

    @Autowired
    private DockTurnAroundTimeService dockTurnAroundTimeService;

    private static final Logger LOGGER = LoggerFactory.getLogger(TurnAroundTimeController.class);


    @GetMapping("/client/{clientId}")
    public ResponseEntity<List<DockTurnAroundTimeDTO>> getAverageTurnAroundTime(@PathVariable String clientId,
                                                                          @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {
        LOGGER.info(">> getAverageTurnAroundTime : ClientId  , isRefresh : ({}, {})", clientId, isRefresh);
        long start = System.currentTimeMillis();
        List<DockTurnAroundTimeDTO> turnAroundTimes = dockTurnAroundTimeService.calculateAverageTurnAroundTime(clientId, isRefresh);
        LoggerUtil.logSLA(LOGGER, "getAverageTurnAroundTime", start, "getAverageTurnAroundTime completed");
        return ResponseEntity.ok(turnAroundTimes);
    }

}
