package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.DayStats;
import com.ma.spoton.api.dtos.InTransitSpots;
import com.ma.spoton.api.dtos.SpotCountsDTO;
import com.ma.spoton.api.dtos.SpotIdNameDTO;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.dtos.WeeklyTotalSpotsDTO;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.DashboardService;
import com.ma.spoton.api.utils.LoggerUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api("Dashboard APIs")
@RestController
@RequestMapping("/v1/dashboard")
public class DashboardController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DashboardController.class);

    @Autowired
    private DashboardService dashboardService;

    /**
     * Method returns the average moves of the client for 3 weeks.
     *
     * @param clientId  - UUID of the Client Id
     * @param isRefresh - Boolean used in the caching workflow
     * @return Integer value of average moves for the given client
     */
    @ApiOperation("This API returns the client's average move count, based on a default averaging window of 3 weeks.")
    @GetMapping("/avg-moves")
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + IT + "', '" + SPOTTER + "', '" + DRIVER
                  + "', '" + SUPERVISOR + "', '" + GUARD + "')")
    public ResponseEntity<Map<String, Integer>> getAverageMoves(
            @RequestParam() String clientId,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {

        long start = System.currentTimeMillis();

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        LOGGER.info(">> getAverageMoves({}, {})", clientId, user.getUserId());

        Map<String, Integer> res = dashboardService.getAvgMovesOfClient(clientId, user.getTimeZone(), isRefresh);
        LoggerUtil.logSLA(LOGGER, "getDailyHourlyMovesAvg", start, "getDailyHourlyMovesAvg completed");

        return ResponseEntity.ok(res);
    }

    /**
     * Method returns the average moves of the client for every hour in a day.
     *
     * @param clientId  - UUID of the Client Id
     * @param isRefresh - Boolean used in the caching workflow
     * @return Integer value of average moves for the given client
     */
    @ApiOperation("This API returns the client’s average daily moves for each hour, calculated over the last 7 days by default.")
    @GetMapping("/daily-hourly-move-average")
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + IT + "', '" + SPOTTER + "', '" + DRIVER
                  + "', '" + SUPERVISOR + "', '" + GUARD + "')")
    public ResponseEntity<DayStats> getDailyHourlyMovesAvg(
            @RequestParam() String clientId,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {

        long start = System.currentTimeMillis();

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        LOGGER.info(">> getDailyHourlyMovesAvg({}, {})", clientId, user.getUserId());

        DayStats res = dashboardService.getHourlyAvgMoves(clientId, user.getTimeZone(), isRefresh);
        LoggerUtil.logSLA(LOGGER, "getDailyHourlyMovesAvg", start, "getDailyHourlyMovesAvg completed");

        return ResponseEntity.ok(res);
    }

    /**
     * API to return spots that are In transit for a particular client.
     */
    @ApiOperation(value = "This API returns the client's spots that are In transit", tags = {"in-transit-spots"})
    @GetMapping("/in-transit-spots")
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + IT + "', '" + SPOTTER + "', '" + DRIVER
                  + "', '" + SUPERVISOR + "', '" + GUARD + "')")
    public ResponseEntity<InTransitSpots> getInTransitSpots(
            @RequestParam() String clientId,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {

        long start = System.currentTimeMillis();

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        LOGGER.info(">> getInTransitSpots({}, {})", clientId, user.getUserId());

        InTransitSpots inTransitSpots = dashboardService.getInTransitSpots(clientId, user.getTimeZone(), isRefresh);
        LoggerUtil.logSLA(LOGGER, "getInTransitSpots", start, "getInTransitSpots completed");

        return ResponseEntity.ok(inTransitSpots);
    }

    /**
     * API to return spots that are Completed in weekStart's (or previous week's) Monday to Sunday for a particular
     * client.
     */
    @ApiOperation(value = "This API returns the client's spots that are Completed from weekStart(Monday) to " +
                          "weekEnd(Sunday). By default, it returns previous week data if the weekStart is not provided",
            tags = {"weekly-total-spots"})
    @GetMapping("/weekly-total-spots")
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + IT + "', '" + SPOTTER + "', '" + DRIVER
                  + "', '" + SUPERVISOR + "', '" + GUARD + "')")
    public ResponseEntity<WeeklyTotalSpotsDTO> getWeeklyTotalSpots(
            @RequestParam() String clientId,
            @RequestParam(required = false, defaultValue = "") String weekStart,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {

        long start = System.currentTimeMillis();

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        LOGGER.info(">> getPreviousWeekTotalSpots({}, {})", clientId, user.getUserId());

        WeeklyTotalSpotsDTO weeklyTotalSpots =
                dashboardService.getWeeklyTotalSpots(clientId, user.getTimeZone(), weekStart, isRefresh);
        LoggerUtil.logSLA(LOGGER, "getPreviousWeekTotalSpots", start, "getPreviousWeekTotalSpots completed");

        return ResponseEntity.ok(weeklyTotalSpots);
    }

    // TODO: Currently this API is used only in dashboard page, if the api needs to be used elsewhere,
    //  then move it to respective controller/service/jpaRepository.. (or under respective path, for eg. LocationController - /v1/clients/{clientId}/locations')

    /**
     * API to return all spots of a particular location.
     */
    @ApiOperation(value = "This API is used to get all spots of a particular location", tags = {"spots-of-location"})
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/spots-of-location")
    public ResponseEntity<List<SpotIdNameDTO>> getAllSpotsOfLocation(
            @RequestParam() String locationId,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {

        long start = System.currentTimeMillis();

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        LOGGER.info(">> getAllSpotsOfLocation({}, {})", user.getId(), user.getUserId());

        List<SpotIdNameDTO> allSpots = dashboardService.getAllSpotsOfLocation(locationId, isRefresh);
        LoggerUtil.logSLA(LOGGER, "getAllSpotsOfLocation", start, "getAllSpotsOfLocation completed");

        return ResponseEntity.ok(allSpots);
    }

    /**
     * API to return metric data like particular spot completed count in last x days (7 by default) , all spots of the
     * client completed count along with percentage
     */
    @ApiOperation(value =
            "This API is used to get all Completed spot count for a given spot in last x days (7 default) " +
            "along with Completed spot count of the client (all spots) in last x days " +
            "and completed percentage.",
            tags = {"spot-counts"})
    @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + IT + "', '" + SPOTTER + "', '" + DRIVER
                  + "', '" + SUPERVISOR + "', '" + GUARD + "')")
    @GetMapping("/spot-counts")
    public ResponseEntity<SpotCountsDTO> getSpotCountsOfDays(
            @RequestParam() String clientId,
            @RequestParam() String spotId,
            @RequestParam(required = false, defaultValue = "7") Integer daysBefore,
            @RequestParam(required = false, defaultValue = "false") boolean isRefresh) {

        long start = System.currentTimeMillis();

        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        LOGGER.info(">> getSpotCountsOfDays({}, {})", user.getId(), user.getUserId());

        SpotCountsDTO spotCounts = dashboardService.getSpotCountsOfDays(clientId, user.getTimeZone(),
                                                                        spotId, daysBefore, isRefresh);
        LoggerUtil.logSLA(LOGGER, "getSpotCountsOfDays", start, "getSpotCountsOfDays completed");

        return ResponseEntity.ok(spotCounts);
    }
}
