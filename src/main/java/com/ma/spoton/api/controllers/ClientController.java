package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.validation.ConstraintViolationException;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.ClientConfigDto;
import com.ma.spoton.api.dtos.ClientDto;
import com.ma.spoton.api.dtos.FleetDto;
import com.ma.spoton.api.dtos.FleetMergeDto;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.dtos.UserDto;
import com.ma.spoton.api.entities.Client;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.mappers.UserMapper;
import com.ma.spoton.api.repositories.ClientRepository;
import com.ma.spoton.api.repositories.UserRepository;
import com.ma.spoton.api.requests.ClientConfigRequest;
import com.ma.spoton.api.requests.ClientFleetRequest;
import com.ma.spoton.api.requests.ClientRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.ClientService;
import com.ma.spoton.api.services.FleetService;
import com.ma.spoton.api.services.UserService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Clients APIs")
@RestController
@RequestMapping("/v1/clients")
public class ClientController {

  @Autowired
  private ClientService clientService;

  @Autowired
  private FleetService fleetService;

  @Autowired
  private UserService userService;
  
  @Autowired
  private UserRepository userrepository;
 
  @Autowired
  private ClientRepository clientRepository;
  
  private static final Logger LOGGER = LoggerFactory.getLogger(ClientController.class);
  
  @Secured({IT, ADMIN})
  @ApiOperation(value = "This API is used to create new Client", code = 201,
      consumes = "application/json")
  @PostMapping
  public ResponseEntity<Void> createClient(@Valid @RequestBody ClientRequest clientRequest) {
	long start = System.currentTimeMillis();
    clientService.createClient(clientRequest);
    LoggerUtil.logSLA(LOGGER, "createClient", start, "createClient completed");
    return ResponseEntity.status(HttpStatus.CREATED).build();
  }

  @ApiOperation("This API is used to update Client")
  @PutMapping("/{clientId}")
  @Secured({IT, ADMIN})
  public ResponseEntity<Void> updateClient(@PathVariable String clientId,
      @Valid @RequestBody ClientRequest clientRequest) {
	long start = System.currentTimeMillis();
    try {
      clientService.updateClient(clientId, clientRequest);
    } catch (ConstraintViolationException | DataIntegrityViolationException e) {
      log.error("Error occurred while updating client! Reason : {}", e.getMessage());
      throw new ServiceException(ErrorCode.DUPLICATE_CLIENT_NAME);
    }
    LoggerUtil.logSLA(LOGGER, "updateClient", start, "updateClient completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

  @ApiOperation("This API is used to delete Client")
  @DeleteMapping("/{clientId}")
  @Secured({IT, ADMIN})
  public ResponseEntity<Void> deleteClient(@PathVariable String clientId) {
	long start = System.currentTimeMillis();
    clientService.deleteClient(clientId);
    LoggerUtil.logSLA(LOGGER, "deleteClient", start, "deleteClient completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

  @ApiOperation("This API is used to get Client by ID")
  @GetMapping("/{clientId}")
  @PreAuthorize("isAuthenticated()")
  public ResponseEntity<ClientDto> getClient(@PathVariable String clientId) {
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    ClientDto resource =  clientService.getClient(clientId, user.getTimeZone(), user.getClientIds());
    LoggerUtil.logSLA(LOGGER, "getClient", start, "getClient completed");
    return ResponseEntity.ok(resource);
    /*return ResponseEntity
        .ok(clientService.getClient(clientId, user.getTimeZone(), user.getClientIds()));*/
  }

  @ApiOperation("This API is used to get paginated list of Clients")
  @GetMapping
  @PreAuthorize("isAuthenticated()")
  public ResponseEntity<PagedResponse<ClientDto>> getClients(
      @QuerydslPredicate(root = Client.class) Predicate predicate, Pageable pageable) {
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    User user1=userrepository.findByUserUuid(user.getUserId());
    user1.setLastActiveTime(ZonedDateTime.now());
    userrepository.save(user1);
    List<String> clientIds=new ArrayList<>();
    List<Client> clients=clientRepository.findAllByUserId(user1.getId());
    for(Client client:clients)
    {
    	clientIds.add(client.getUuid());
    }

    PagedResponse<ClientDto> resource =  clientService.getClients(predicate, pageable, user.getTimeZone(), clientIds);
    LoggerUtil.logSLA(LOGGER, "getClients", start, "getClients completed");
    return ResponseEntity.ok(resource);
    /*return ResponseEntity
          .ok(clientService.getClients(predicate, pageable, user.getTimeZone(), clientIds));*/
  
  }

  @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + SPOTTER + "', '" + DRIVER
      + "', '" + SUPERVISOR + "', '" + GUARD + "')")
  @ApiOperation("This API is used to list of Fleets by ClientID")
  @GetMapping("/{clientId}/fleets")
  public ResponseEntity<PagedResponse<FleetDto>> getClientFleets(@PathVariable String clientId,
      @QuerydslPredicate(root = Fleet.class) Predicate predicate, Pageable pageable,
      @RequestParam(required = false) String locationIds, @RequestParam(required = false) String unit_sequenceNumber) {
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    PagedResponse<FleetDto> resource =  fleetService.getFleets(predicate, pageable, user.getTimeZone(), List.of(clientId),locationIds, unit_sequenceNumber);
    LoggerUtil.logSLA(LOGGER, "getClientFleets", start, "getClientFleets completed");
    return ResponseEntity.ok(resource);
    /*return ResponseEntity
        .ok(fleetService.getFleets(predicate, pageable, user.getTimeZone(), List.of(clientId)));*/
  }

  @PreAuthorize("hasAnyClientRole(#clientId, '" + CLIENT + "', '" + SPOTTER + "', '" + DRIVER
      + "', '" + SUPERVISOR + "', '" + GUARD + "')")
  @ApiOperation("This API is used to list of Users by ClientID")
  @GetMapping("/{clientId}/users")
  public ResponseEntity<PagedResponse<UserDto>> getClientUsers(@PathVariable String clientId, @RequestParam(required = false) String locationIds,
		  @RequestParam(required = false) String roleName, @QuerydslPredicate(root = User.class) Predicate predicate, Pageable pageable) {
	  long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    PagedResponse<UserDto> resource = userService.getUsers(predicate, pageable, user.getTimeZone(), List.of(clientId),roleName,null, locationIds);
    LoggerUtil.logSLA(LOGGER, "getClientUsers", start, "getClientUsers completed");
    return ResponseEntity.ok(resource);
    /*return ResponseEntity
        .ok(userService.getUsers(predicate, pageable, user.getTimeZone(), List.of(clientId),roleName,null, locationIds));*/
  }

  @PreAuthorize("hasAnyClientRole(#clientId, '" + GUARD + "')")
  @ApiOperation("This API is used to assign Fleets to Client")
  @PostMapping("/{clientId}/fleets")
  public ResponseEntity<Void> assignFleets(@PathVariable String clientId,
      @Valid @RequestBody ClientFleetRequest clientFleetRequest) {
	long start = System.currentTimeMillis();
    fleetService.assignFleets(clientId, clientFleetRequest);
    LoggerUtil.logSLA(LOGGER, "assignFleets", start, "assignFleets completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

  @PreAuthorize("hasAnyClientRole(#clientId, '" + GUARD + "')")
  @ApiOperation("This API is used to unassign Fleets from Client")
  @DeleteMapping("/{clientId}/fleets")
  public ResponseEntity<Void> unassignFleets(@PathVariable String clientId,
      @Valid @RequestBody ClientFleetRequest clientFleetRequest) {
	long start = System.currentTimeMillis();
    fleetService.unassignFleets(clientId, clientFleetRequest);
    LoggerUtil.logSLA(LOGGER, "unassignFleets", start, "unassignFleets completed");
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }

  @ApiOperation("This API is used to export list of Clients as CSV")
  @PreAuthorize("isAuthenticated()")
  @GetMapping("/export/csv")
  public ResponseEntity<Resource> exportClientsAsCsv(
      @QuerydslPredicate(root = Client.class) Predicate predicate) {
	long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    Resource resource =
        clientService.exportClientsAsCSV(predicate, user.getTimeZone(), user.getClientIds());
    LoggerUtil.logSLA(LOGGER, "exportClientsAsCsv", start, "exportClientsAsCsv completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "SpotOn_Clients.csv")
        .contentType(MediaType.parseMediaType("text/csv")).body(resource);
  }
  
  
  @ApiOperation("This API is used to export list of Clients as EXCEL")
  @PreAuthorize("isAuthenticated()")
  @GetMapping("/export/excel")
  public ResponseEntity<Resource> exportClientsAsExcel(
      @QuerydslPredicate(root = Client.class) Predicate predicate) {
	  long start = System.currentTimeMillis();
    UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
    Resource resource =
        clientService.exportClientsAsExcel(predicate, user.getTimeZone(), user.getClientIds());
    LoggerUtil.logSLA(LOGGER, "exportClientsAsExcel", start, "exportClientsAsExcel completed");
    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Clients_Report.xlsx")
        .contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
  }
  
 
  
  @Secured({IT, ADMIN,SUPERVISOR})
  @ApiOperation("This API is used to merge fleets")
  @PostMapping("/fleets/merge")
  public  ResponseEntity<Void> mergeUnitNumber(@RequestBody FleetMergeDto fleetMergeDto )
  {
	  long start = System.currentTimeMillis();
	  boolean isMerged=fleetService.mergeFleet(fleetMergeDto);
	  if(isMerged==true)
	  {
		  fleetService.assignFleetsAfterMerging(fleetMergeDto);
	  }
	  LoggerUtil.logSLA(LOGGER, "mergeUnitNumber", start, "mergeUnitNumber completed");
	  return ResponseEntity.status(HttpStatus.NO_CONTENT).build(); 
  }
  
  @ApiOperation("This API is used to update Client Config")
  @PostMapping("/config")
  @Secured({IT, ADMIN})
  public ResponseEntity<Void> updateClientConfig(@RequestBody ClientConfigRequest clientConfigRequest) {
    try {
      long start = System.currentTimeMillis();	
      clientService.updateClientConfig(clientConfigRequest);
      LoggerUtil.logSLA(LOGGER, "updateClientConfig", start, "updateClientConfig completed");
    } catch (ConstraintViolationException | DataIntegrityViolationException e) {
      log.error("Error occurred while updating client! Reason : {}", e.getMessage());
      throw new ServiceException(ErrorCode.CONFIG_UPDATE_ERROR);
    }
    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
  }
  
  
  @GetMapping("/config")
  public ResponseEntity<ClientConfigDto> getClientConfig() {
	    try {
	    	long start = System.currentTimeMillis();
	    	UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
	    	userService.updateLastActiveTime(user.getUserId());
	    	if(user.getAuthorities().contains(IT) || user.getAuthorities().contains(ADMIN))
	    	{
	    		
	    		return ResponseEntity.ok(null);
	    	}
	    	ClientConfigDto resource =	clientService.getClientConfig(user.getUserId());
	    	LoggerUtil.logSLA(LOGGER, "getClientConfig", start, "getClientConfig completed");
	    	return ResponseEntity.ok(resource);
	    	
	        /*return ResponseEntity
	                .ok(clientService.getClientConfig(user.getUserId()));*/
	    } catch (ConstraintViolationException | DataIntegrityViolationException e) {
	      log.error("Error occurred while updating client! Reason : {}", e.getMessage());
	      throw new ServiceException(ErrorCode.CONFIG_UPDATE_ERROR);
	    }
	    
	  }
  
  
  @PreAuthorize("isAuthenticated()")
  @GetMapping("/{clientId}/clientConfig")
  public ResponseEntity<ClientConfigDto> getConfigClient(@PathVariable String clientId){
	  long start = System.currentTimeMillis();
	  ClientConfigDto resource = clientService.getConfigOfClient(clientId);
	  LoggerUtil.logSLA(LOGGER, "getConfigOfClient", start, "getConfigOfClient completed");
	  return ResponseEntity.ok(resource);
	  //return ResponseEntity.ok(clientService.getConfigOfClient(clientId));
  }
  

}
