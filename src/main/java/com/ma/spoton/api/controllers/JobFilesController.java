package com.ma.spoton.api.controllers;
import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;

import java.io.IOException;

import javax.validation.ConstraintViolationException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.ClientDto;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.BolRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.ClientService;
import com.ma.spoton.api.services.FileService;
import com.ma.spoton.api.utils.LoggerUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("Client Config APIs")
@RestController
public class JobFilesController {
	 @Autowired
	  private ClientService clientService;
	  
	  @Autowired
	  private FileService fileService;
	  
	  private static final Logger LOGGER = LoggerFactory.getLogger(JobFilesController.class);
	  
	  @ApiOperation("This API is used to save BOL")
	  @PostMapping(value="/v1/bol",consumes = {MediaType.APPLICATION_JSON_VALUE,MediaType.MULTIPART_FORM_DATA_VALUE})
	  @Secured({DRIVER})
	  public ResponseEntity<Void> insertBol(BolRequest bolrequest) throws IOException {
		  
	    try {
	    	long start = System.currentTimeMillis();
	    	String bol="BOL";
	        fileService.createFolderAndSaveBol(bol,bolrequest.getFiles(),bolrequest.getJobId(),bolrequest.getType());
	        LoggerUtil.logSLA(LOGGER, "insertBol", start, "insertBol completed");
	    } catch (ConstraintViolationException | DataIntegrityViolationException e) {
	      log.error("Error occurred while uploading bol : {}", e.getMessage());
	      throw new ServiceException(ErrorCode.BOL_ERROR);
	    }
	    return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
	  }
}
