package com.ma.spoton.api.controllers;

import static com.ma.spoton.api.constants.SystemRoles.ADMIN;
import static com.ma.spoton.api.constants.SystemRoles.IT;
import static com.ma.spoton.api.constants.SystemRoles.SUPERVISOR;
import static com.ma.spoton.api.constants.SystemRoles.CLIENT;
import static com.ma.spoton.api.constants.SystemRoles.DRIVER;
import static com.ma.spoton.api.constants.SystemRoles.SPOTTER;
import static com.ma.spoton.api.constants.SystemRoles.GUARD;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.ConstraintViolationException;
import javax.validation.Valid;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ma.spoton.api.dtos.ClientUpdateDTO;
import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.RoleDto;
import com.ma.spoton.api.dtos.RoleUpdateDTO;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.dtos.UserDto;
import com.ma.spoton.api.dtos.UserUpdateDTO;
import com.ma.spoton.api.entities.Role;
import com.ma.spoton.api.entities.User;
import com.ma.spoton.api.exception.ErrorCode;
import com.ma.spoton.api.exception.ServiceException;
import com.ma.spoton.api.requests.ActivateInactiveUserRequest;
import com.ma.spoton.api.requests.ActivateUserRequest;
import com.ma.spoton.api.requests.RegisterUserDeviceRequest;
import com.ma.spoton.api.requests.UserRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.UserService;
import com.querydsl.core.types.Predicate;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import com.ma.spoton.api.utils.LoggerUtil;

@Slf4j
@Api("Users APIs")
@RestController
@RequestMapping("/v1/users")
public class UserController {

	@Autowired
	private UserService userService;

	@Value("${application.api-base-url}")
	private String apiBaseUrl;

	@Value("${application.version}")
	private String version;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(UserController.class);

	@Secured({ IT, ADMIN, SUPERVISOR })
	@ApiOperation(value = "This API is used to create new User", code = 201, consumes = "application/json")
	@PostMapping
	public ResponseEntity<Map<String, Object>> createUser(@Valid @RequestBody UserRequest userRequest) {
		long start = System.currentTimeMillis();
		validateSupervisorUser(userRequest);
		User user = userService.createUser(userRequest);
		LoggerUtil.logSLA(LOGGER, "CREATE_USER", start, "Create User complete");
		return ResponseEntity.created(URI.create(String.format("%s/%s/users/%s", apiBaseUrl, version, user.getUuid())))
				.body(Map.of("email", user.getEmail()));
	}

	@Secured({ IT, ADMIN, SUPERVISOR, CLIENT, DRIVER, GUARD, SPOTTER })
	@ApiOperation(value = "This API is used to update User", code = 204, consumes = "application/json")
	@PutMapping("/{userId}")
	public ResponseEntity<Void> updateUser(@PathVariable String userId, @Valid @RequestBody UserRequest userRequest) {
		long start = System.currentTimeMillis();
		validateSupervisorUser(userService.getUser(userId));
		validateSupervisorUser(userRequest);

		try {
			userService.updateUser(userId, userRequest);
			LoggerUtil.logSLA(LOGGER, "UPDATE_USER", start, "Update User completed");
			return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
		} catch (ConstraintViolationException | DataIntegrityViolationException e) {
			log.error("Error occurred while updating User! Reason : {}", e.getMessage());
			throw new ServiceException(ErrorCode.CREATE_UPDATE_USER_DUPLICATE_EMAIL);
		}
	}

	@Secured({ IT, ADMIN, SUPERVISOR })
	@ApiOperation(value = "This API is used to delete / deactivate User", code = 204)
	@DeleteMapping("/{userId}")
	public ResponseEntity<Void> deleteUser(@PathVariable String userId) {
		
		long start = System.currentTimeMillis();
		if (AuthDetailsProvider.getLoggedInUser().getUserId().equals(userId)) {
			throw new ServiceException(ErrorCode.UPDATE_DELETE_SELF_USER);
		}

		validateSupervisorUser(userService.getUser(userId));

		userService.deleteUser(userId);
		
		LoggerUtil.logSLA(LOGGER, "DELETE_USER", start, "Delete User completed");
		return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
	}

	/**
	 * Driver, yards spotters for the scheduled workflows
	 * @return
	 */
	@PreAuthorize("isAuthenticated()")
	@ApiOperation("This API is used to list of buckets for the scheduled workflows")
	@GetMapping("/scheduledusers/buckets")
	public ResponseEntity<List<String>> getScheduledBuckets() {
		long start = System.currentTimeMillis();
		List<String> resource = userService.getScheduledBuckets();
		LoggerUtil.logSLA(LOGGER, "getScheduledBuckets", start, "getScheduledBuckets completed");
		return ResponseEntity.ok(resource);
   }

	@ApiOperation("This API is used to get User by ID")
	@GetMapping("/{userId}")
	@PreAuthorize("isAuthenticated()")
	public ResponseEntity<UserDto> getUser(@PathVariable String userId) {
		long start = System.currentTimeMillis();
		UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
		UserDto resource = userService.getUser(userId, user.getTimeZone(), user.getClientIds());
		LoggerUtil.logSLA(LOGGER, "GET_USER", start, "GET User completed");
		return ResponseEntity.ok(resource);
	}

	@ApiOperation("This API is used to get new User by ID")
	@GetMapping("/newUser/{newUserId}")
	public ResponseEntity<UserDto> getNewUser(@PathVariable String newUserId) {
		long start = System.currentTimeMillis();
		
		UserDto resource = userService.getNewUser(newUserId);
		
		LoggerUtil.logSLA(LOGGER, "GET_NEW_USER", start, "Get New User completed");
		return ResponseEntity.ok(resource);
	}

	@ApiOperation("This API is used set password for new user")
	@PostMapping("/activate")
	public ResponseEntity<Void> activateUser(@RequestBody ActivateUserRequest activateUserRequest) {
		long start = System.currentTimeMillis();
		
		userService.activateUser(activateUserRequest);
		
		LoggerUtil.logSLA(LOGGER, "ACTIVATE_USER", start, "Activate user complete");
		return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
	}

	@ApiOperation("This API is used to get paginated list of Users")
	@GetMapping
	@PreAuthorize("isAuthenticated()")
	public ResponseEntity<PagedResponse<UserDto>> getUsers(@RequestParam(required = false) String roleName, @RequestParam(required = false) String locationIds,
			@RequestParam(required = false) Boolean allUsers,@QuerydslPredicate(root = User.class) Predicate predicate,
			Pageable pageable) {
		long start = System.currentTimeMillis();
		UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
		PagedResponse<UserDto> resource = userService.getUsers(predicate, pageable, user.getTimeZone(), user.getClientIds(), roleName, allUsers, locationIds);
		LoggerUtil.logSLA(LOGGER, "GET_USERS_LIST", start, "Get  User List completed");
		return ResponseEntity.ok(resource);
		/*return ResponseEntity
			.ok(userService.getUsers(predicate, pageable, user.getTimeZone(), user.getClientIds(), roleName, allUsers, locationIds));*/
	}

	@Secured({ IT, ADMIN, SUPERVISOR, CLIENT, DRIVER, GUARD, SPOTTER })
	@ApiOperation(value = "This API is used to reset User Password")
	@PatchMapping("/{userId}/resetPassword")
	public ResponseEntity<Map<String, String>> resetUserPassword(@PathVariable String userId) {
		
		long start = System.currentTimeMillis();
		/*if (AuthDetailsProvider.getLoggedInUser().getUserId().equals(userId)) {
      		throw new ServiceException(ErrorCode.UPDATE_DELETE_SELF_USER);
		}*/

		validateSupervisorUser(userService.getUser(userId));

		String tempPassword = userService.resetUserPassword(userId);
		
		LoggerUtil.logSLA(LOGGER, "RESET_PASSWORD", start, "Reset User password completed");
		return ResponseEntity.ok(Map.of("password", tempPassword));
	}

	@ApiOperation("This API is used to export list of Users as CSV")
	@PreAuthorize("isAuthenticated()")
	@GetMapping("/export/csv")
	public ResponseEntity<Resource> exportUsersAsCsv(@QuerydslPredicate(root = User.class) Predicate predicate) {
		long start = System.currentTimeMillis();
		
		UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
		Resource resource = userService.exportUsersAsCSV(predicate, user.getTimeZone(), user.getClientIds());
		
		LoggerUtil.logSLA(LOGGER, "EXPORT_USERS_LIST_CSV", start, "Exports Users List CSV complete");
		return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "SpotOn_Users.csv")
				.contentType(MediaType.parseMediaType("text/csv")).body(resource);
	}

	@ApiOperation("This API is used to export list of Users as EXCEL")
	@PreAuthorize("isAuthenticated()")
	@GetMapping("/export/excel")
	public ResponseEntity<Resource> exportUsersAsExcel(@QuerydslPredicate(root = User.class) Predicate predicate) {
		long start = System.currentTimeMillis();
		
		UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
		Resource resource = userService.exportUsersAsEXCEL(predicate, user.getTimeZone(), user.getClientIds());
		
		LoggerUtil.logSLA(LOGGER, "EXPORT_USERS_LIST_EXCEL", start, "Exports Users List EXCEL completed");
		return ResponseEntity.ok()
				.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Users_Report.xlsx")
				.contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
	}

	@PreAuthorize("isAuthenticated()")
	@ApiOperation(value = "This API is used to register User Device.", code = 200)
	@PostMapping("/devices")
	public ResponseEntity<Void> registerUserDevice(@Valid @RequestBody RegisterUserDeviceRequest userDeviceRequest) {
		long start = System.currentTimeMillis();
		
		userService.createOrUpdateUserDevice(userDeviceRequest, AuthDetailsProvider.getLoggedInUser().getUserId());
		
		LoggerUtil.logSLA(LOGGER, "REGISTER_USER", start, "Register Users completed");
		return ResponseEntity.ok().build();
	}

	@PreAuthorize("isAuthenticated()")
	@ApiOperation(value = "This API is used to send mail to inactive users for account activation", code = 200)
	@PostMapping("/inactive")
	public ResponseEntity<Void> activateInactiveAccount(
			@RequestBody ActivateInactiveUserRequest activateInactiveUserRequest) {
		long start = System.currentTimeMillis();
		
		userService.sendMailtoInactiveUser(activateInactiveUserRequest);
		LoggerUtil.logSLA(LOGGER, "ACTIVATE_INACTIVE_ACCT", start, "Activate Inactive account completed");
		return ResponseEntity.ok().build();
	}
	
	@ApiOperation("This API is used to get all roles")
    @GetMapping("/roles")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<RoleDto>> getRoles(@RequestParam(required = false) String client) {
        long start = System.currentTimeMillis();
        UserAuthDto user = AuthDetailsProvider.getLoggedInUser();
        List<RoleDto> roles = userService.getRoles(user.getClientIds(), client);
        LoggerUtil.logSLA(LOGGER, "getRoles", start, "getRoles completed");
        return ResponseEntity.ok(roles);
    }

	private void validateSupervisorUser(UserRequest userRequest) {
		if (AuthDetailsProvider.isRolePresent(SUPERVISOR)) {
			if (!CollectionUtils.isSubCollection(userRequest.getClientIds(),
					AuthDetailsProvider.getLoggedInUser().getClientIds())) {
				throw new ServiceException(ErrorCode.MANAGE_USER_OTHER_CLIENT_ACCESS_DENIED);
			}
			List<String> allowedRoleIds = userService
					.getRolesByNames(List.of("DRIVER", "GUARD", "SPOTTER", "CLIENT", "SUPERVISOR")).stream()
					.map(Role::getUuid).collect(Collectors.toList());
			if (!CollectionUtils.isSubCollection(userRequest.getRoleIds(), allowedRoleIds)) {
				throw new ServiceException(ErrorCode.MANAGE_USER_OTHER_ROLE_ACCESS_DENIED);
			}
		}
	}

	private void validateSupervisorUser(UserUpdateDTO user) {
		
		if (AuthDetailsProvider.isRolePresent(SUPERVISOR)) {
			

			List<String> userClientIds = CollectionUtils.isNotEmpty(user.getClients())
					? user.getClients().stream().map(ClientUpdateDTO::getUuid).collect(Collectors.toList())
					: List.of();

			if (CollectionUtils.isEmpty(userClientIds) || !CollectionUtils.isSubCollection(userClientIds,
					AuthDetailsProvider.getLoggedInUser().getClientIds())) {
				throw new ServiceException(ErrorCode.MANAGE_USER_OTHER_CLIENT_ACCESS_DENIED);
			}

			// Check for null or initialize roles to avoid NullPointerException
			List<String> userRoleIds = CollectionUtils.isNotEmpty(user.getRoles())
					? user.getRoles().stream().map(RoleUpdateDTO::getUuid).collect(Collectors.toList())
					: List.of();

			List<String> allowedRoleIds = userService
					.getRolesByNames(List.of("DRIVER", "GUARD", "SPOTTER", "CLIENT", "SUPERVISOR")).stream()
					.map(Role::getUuid).collect(Collectors.toList());

			if (!CollectionUtils.isSubCollection(userRoleIds, allowedRoleIds)) {
				throw new ServiceException(ErrorCode.MANAGE_USER_OTHER_ROLE_ACCESS_DENIED);
			}
		}
	}

}
