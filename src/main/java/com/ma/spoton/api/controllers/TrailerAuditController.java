package com.ma.spoton.api.controllers;

import com.ma.spoton.api.dtos.PagedResponse;
import com.ma.spoton.api.dtos.TrailerAuditDto;
import com.ma.spoton.api.dtos.UserAuthDto;
import com.ma.spoton.api.entities.Job.FleetStatus;
import com.ma.spoton.api.entities.Spot;
import com.ma.spoton.api.entities.TrailerAudit;
import com.ma.spoton.api.requests.TrailerAuditRequest;
import com.ma.spoton.api.security.AuthDetailsProvider;
import com.ma.spoton.api.services.TrailerAuditService;
import com.ma.spoton.api.utils.LoggerUtil;
import com.querydsl.core.types.Predicate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URI;

import static com.ma.spoton.api.constants.SystemRoles.*;

@Slf4j
@Api("Trailer Audit api")
@RestController
@RequestMapping("/v1/trailerAudit")
public class TrailerAuditController {

	@Value("${application.api-base-url}")
	private String apiBaseUrl;

	@Value("${application.version}")
	private String version;

	@Autowired
	private TrailerAuditService trailerAuditService;
	
	private static final Logger LOGGER = LoggerFactory.getLogger(TrailerAuditController.class);

	@Secured({SUPERVISOR, CLIENT, ADMIN, IT, DRIVER, SPOTTER, GUARD})
	@ApiOperation(value = "This API is used to create new Trailer Audit", code = 201,
		consumes = "application/json")
	@PostMapping
	public ResponseEntity<Void> createTrailerAudit(@Valid @RequestBody TrailerAuditRequest[] trailerAuditRequests) {
		
		long start = System.currentTimeMillis();
		trailerAuditService.createTrailerAudit(trailerAuditRequests);
		LoggerUtil.logSLA(LOGGER, "createTrailerAudit", start, "createTrailerAudit completed");
		return ResponseEntity
			.created(URI.create(String.format("%s/%s/jobs", apiBaseUrl, version)))
			.build();
	}
	
	@Secured({SUPERVISOR, CLIENT, ADMIN, IT, DRIVER, SPOTTER, GUARD})
	@PatchMapping("/{spotId}")
	public ResponseEntity<Void> clearSpot(@PathVariable String spotId) {
		
		
		trailerAuditService.clearSpot(spotId);
		return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
	}


	@ApiOperation("This API is used to export list of Trailer Audit as EXCEL")
	@Secured({CLIENT, SUPERVISOR, IT, ADMIN})
	@GetMapping("/export/excel")
	public ResponseEntity<Resource> exportTrailerAuditsAsExcel(@QuerydslPredicate(root = Spot.class) Predicate predicate,
													  @RequestParam(required = true) String clientId, @RequestParam(required = false) String locationIds,
													  @RequestParam(required = false) FleetStatus fleetStatus) {
		
		long start = System.currentTimeMillis();
		UserAuthDto loggedInUser = AuthDetailsProvider.getLoggedInUser();
		Resource resource = trailerAuditService.exportTrailerAuditAsEXCEL(clientId, locationIds, fleetStatus, predicate, loggedInUser.getTimeZone());
		LoggerUtil.logSLA(LOGGER, "exportTrailerAuditAsExcel", start, "exportTrailerAuditAsExcel completed");
		return ResponseEntity.ok()
			.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Trailer_Audit_Report.xlsx")
			.contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
	}

	@ApiOperation("This API is used to export list of Trailer Audit as EXCEL")
	@Secured({CLIENT, SUPERVISOR, IT, ADMIN})
	@GetMapping("/export/pdf")
	public ResponseEntity<Resource> exportTrailerAuditsAsPdf(@QuerydslPredicate(root = Spot.class) Predicate predicate,
													  @RequestParam(required = true) String clientId, @RequestParam(required = false) String locationIds,
													  @RequestParam(required = false) FleetStatus fleetStatus) {
		
		long start = System.currentTimeMillis();
		UserAuthDto loggedInUser = AuthDetailsProvider.getLoggedInUser();
		Resource resource = trailerAuditService.exportTrailerAuditAsPdf(clientId, locationIds, fleetStatus, predicate, loggedInUser.getTimeZone());
		LoggerUtil.logSLA(LOGGER, "exportTrailerAuditAsPdf", start, "exportTrailerAuditAsPdf completed");
		return ResponseEntity.ok()
			.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "Trailer_Audit_Report.pdf")
			.contentType(MediaType.parseMediaType("text/xlsx")).body(resource);
	}
}
