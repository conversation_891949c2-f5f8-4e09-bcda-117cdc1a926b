package com.ma.spoton.api.controllers;
import com.ma.spoton.api.dtos.FleetCarrierUpdate;
import com.ma.spoton.api.services.FleetService;
import com.ma.spoton.api.utils.LoggerUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.data.domain.Page;

import java.util.List;
@Slf4j
@Api("Carrier APIs")
@RestController
@RequestMapping("/v1/carrier")
public class CarriersController {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(CarriersController.class);
	
	@Autowired
	private FleetService fleetService;
	@ApiOperation("This API is used to get unique carriers with starting letters")
	@GetMapping("/{carrier}")
	@PreAuthorize("isAuthenticated()")
	public ResponseEntity<List<String>> getAllCarrier(@PathVariable String carrier) {
		long start = System.currentTimeMillis();
		List<String> resource= fleetService.getUniqueCarrier(carrier);
		LoggerUtil.logSLA(LOGGER, "getAllCarrierWithStartingLetter", start, "getAllCarrierWithStartingLetter completed");
		return ResponseEntity.ok(resource);
		//return ResponseEntity.ok(fleetService.getUniqueCarrier(carrier));
	}
	@ApiOperation("This API is used to get unique carriers")
	@GetMapping
	@PreAuthorize("isAuthenticated()")
	public ResponseEntity<List<String>> getAllCarrier() {
		long start = System.currentTimeMillis();		
		List<String> resource= fleetService.getUniqueCarrier();
		LoggerUtil.logSLA(LOGGER, "getAllCarrier", start, "getAllCarrier completed");
		return ResponseEntity.ok(resource);
	}
	@ApiOperation("This API is used to update the carrier name in Fleet")
	@PatchMapping("/update")
	@PreAuthorize("isAuthenticated()")
	public ResponseEntity updateFleetCarrier(@RequestBody FleetCarrierUpdate fleetCarrierUpdate) {
		long start = System.currentTimeMillis();
		int noOfUpdatedFleets = fleetService.updateCarrierInFleet(fleetCarrierUpdate);
		LoggerUtil.logSLA(LOGGER, "updateFleetCarrier", start, "updateFleetCarrier completed");
		return ResponseEntity.ok().body(noOfUpdatedFleets);
	}
	
	 @ApiOperation("This API is used to get Carriers in page")
	 @GetMapping("/list")
	 @PreAuthorize("isAuthenticated()")
	 public ResponseEntity<Page<String>> getAllCarrier(
		        @RequestParam(required = false) String prefix,  // Optional filter parameter
		        Pageable pageable) { // Automatically bound by Spring
		    // Use the service to get the paginated result
		 	long start = System.currentTimeMillis();
		    Page<String> carriersPage = fleetService.getUniqueCarrierPage(prefix, pageable);
		    LoggerUtil.logSLA(LOGGER, "getAllCarrierPage", start, "getAllCarrierPage completed");
		    return ResponseEntity.ok(carriersPage);
		}
}