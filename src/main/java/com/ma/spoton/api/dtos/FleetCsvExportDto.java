package com.ma.spoton.api.dtos;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Fleet.Type;
import com.ma.spoton.api.entities.Job.FleetStatus;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FleetCsvExportDto {
	
	  private String carrier;
	  private Type type;
	  private String unitNumber;
	  private String remarks;
	  private Boolean isActive;
	  private String owner;
	  private FleetStatus fleetStatus;
	  
	  private String createdDate;
	  private String createdBy;
	  
	  
	  private String locationName;
	  private String spotName;
}
