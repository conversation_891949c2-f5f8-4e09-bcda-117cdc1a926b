package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Fleet.Type;
import com.ma.spoton.api.entities.Job.FleetStatus;
import com.ma.spoton.api.entities.Job.Priority;
import com.ma.spoton.api.entities.Job.Status;
import com.ma.spoton.api.entities.Spot.SpotType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrailerLogExportDto {

	private String createdDate;
	private String actions;
	
	private String pickupDateTime;
	private String dropDateTime;
	private String jobNumber;
	private Priority priority;
	private String pickupLocationName;
	private String pickupSpotName;
	private String dropLocationName;
	private String dropSpotName;
	
	private String description;
	private String fleetUnitNumber;
	private Status status;
	
	private String assignedTo;
	private String createdBy;
	private String lastModifiedDate;
	
}
