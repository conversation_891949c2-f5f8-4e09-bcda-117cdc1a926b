package com.ma.spoton.api.dtos;

import com.ma.spoton.api.entities.Fleet.Type;
import com.ma.spoton.api.entities.Job.FleetStatus;
import com.ma.spoton.api.entities.Location;
import com.ma.spoton.api.entities.Spot.SpotType;
import com.ma.spoton.api.entities.Spot.Status;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

@Data
@Builder
@ToString
public class SpotExportDto {

  private String spotId;
  private String spotName;
  private String locationName;
  private SpotType type;
  private Status status;
  private Double latitude;
  private Double longitude;
  private String remarks;
  private Boolean isActive;
  private Long emptiedSinceSeconds;
  private String fleetCarrier;
  private Type fleetType;
  private String fleetUnitNumber;
  private FleetStatus fleetStatus;

  private String createdDate;
  private String lastModifiedDate;
  private String createdBy;
  private String lastModifiedBy;
  private String notes;
  private Location location;
  
}
