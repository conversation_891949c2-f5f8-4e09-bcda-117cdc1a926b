package com.ma.spoton.api.dtos;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;
import java.util.Set;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Fleet.Type;
import com.ma.spoton.api.entities.Job.FleetStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FleetDto {

  private String fleetId;
  private String carrier;
  private Type type;
  private String unitNumber;
  private String remarks;
  private Boolean isActive;
  private FleetStatus fleetStatus;
  private String owner;
  private Boolean isHotTrailer;
  private SpotDto spot;
  private Set<String> clientIds;
  private String sequenceNumber;
  private Set<String> commodities;
  private Set<SuppliersDto> commodity;
  private Set<ClientDto> clients;

  private AuditDto audit;

}
