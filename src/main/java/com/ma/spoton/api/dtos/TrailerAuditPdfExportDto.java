package com.ma.spoton.api.dtos;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Location;
import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrailerAuditPdfExportDto {

	
	private String area;
	private String slot;
	private String carrier;
	private String trailerNumber;
	private String trailerStatus;
	private String notes;
	private String dateAndTimeEntered;
	private Location location;
}
