package com.ma.spoton.api.dtos;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ma.spoton.api.entities.Job;
import lombok.*;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@JsonInclude(NON_NULL)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FleetStatusCountDto {

    private Job.FleetStatus fleetStatus;
    private Long count;
}

