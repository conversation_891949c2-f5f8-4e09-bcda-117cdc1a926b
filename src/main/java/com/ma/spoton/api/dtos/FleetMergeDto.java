package com.ma.spoton.api.dtos;

import java.util.List;

import com.ma.spoton.api.entities.Fleet.Type;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FleetMergeDto {

	private String selectedUnitNumber;
	private String assignedUnitNumber;
	private List<String> unitNumberList;
	private String client;
	private String owner;
	private Type type;
	private String carrier;
	
}
