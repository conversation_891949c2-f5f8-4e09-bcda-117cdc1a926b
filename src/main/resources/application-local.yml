spring:
  datasource:
    url: ***********************************************
    username: root
    password: rootroot
  jpa:
    database-platform: org.hibernate.dialect.MySQL57Dialect #MySQL8Dialect if MySQL version is 8.x
    generate-ddl: true
    hibernate:
      ddl-auto: update
    #      fail_on_pagination_over_collection_fetch: true
    open-in-view: false
    show-sql: false
  #    properties:
  #      hibernate:
  #        ddl-auto: update
  #        ddl-generation: validate
  #        ddl-generation.output_mode: database
  #        check_nullability: true
  #    datasource:
  #      initialization-mode: always


application:
  admin-portal-base-url: http://localhost:4200
  api-base-url: http://localhost:8080
  weather-api-key: 032e2866b87e9ffd5b25902bbccb59e1
  cors-url: http://localhost:4200
  cors-url-alias: http://localhost:4200
  job:
    auto-send-notification:
      time: 10000
  cdn1: https://d1ipsrrunint6w.cloudfront.net
  cdn:
    base:
      url: https://ablair-spoton.s3.us-east-1.amazonaws.com
    bucket: ablair-spoton
  # 200 Days
  access-token-validity-in-seconds: 17280000
  # 7 Days
  refresh-token-validity-in-seconds: 604800

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.tool.hbm2ddl: DEBUG
